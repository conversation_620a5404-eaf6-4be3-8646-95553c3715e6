# modules/load_gems_passives_to_chroma.py
import chromadb
from sentence_transformers import SentenceTransformer
import os
import re
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Constants
GEMS_FILE_PATH = "data/poe2_gems.txt"
PASSIVES_FILE_PATH = "data/poe2_passive_skills.txt"
CHROMA_DB_BUILD_PATH = "data/chroma_db_build/" # Use the build-specific DB path
GEMS_COLLECTION_NAME = "poe2_gems"
PASSIVES_COLLECTION_NAME = "poe2_passives"
EMBEDDING_MODEL_NAME = 'all-MiniLM-L6-v2' # Consistent embedding model

# --- ChromaDB Client Initialization (Re-use or create) ---
# It's better to pass the client from main.py to avoid multiple initializations
# but we can include a helper if needed.
def _initialize_chroma_client(db_path):
    """Initializes and returns a persistent ChromaDB client."""
    try:
        client = chromadb.PersistentClient(path=db_path)
        logging.info(f"ChromaDB client initialized successfully at path: {db_path}")
        return client
    except Exception as e:
        logging.error(f"Failed to initialize ChromaDB client at {db_path}: {e}")
        raise # Re-raise the exception to handle it upstream

# --- Embedding Model Initialization ---
def _get_embedding_model(model_name=EMBEDDING_MODEL_NAME):
    """Loads and returns the Sentence Transformer model."""
    try:
        model = SentenceTransformer(model_name)
        logging.info(f"Sentence Transformer model '{model_name}' loaded successfully.")
        return model
    except Exception as e:
        logging.error(f"Failed to load Sentence Transformer model '{model_name}': {e}")
        raise

# --- Gem Loading Logic ---
def load_gems_to_db(client, embedding_model, processed_gems_data: list, collection_name=GEMS_COLLECTION_NAME):
    """Loads pre-processed gem data (list of dicts) into a ChromaDB collection."""
    if not processed_gems_data:
        logging.warning("No processed gem data provided. Skipping gem loading.")
        return

    try:
        collection = client.get_or_create_collection(
            name=collection_name,
            metadata={"hnsw:space": "cosine"} # Specify distance metric
        )
        logging.info(f"ChromaDB collection '{collection_name}' accessed/created.")
    except Exception as e:
        logging.error(f"Failed to get or create ChromaDB collection '{collection_name}': {e}")
        return # Cannot proceed without the collection

    logging.info(f"Processing {len(processed_gems_data)} pre-processed gem entries...")
    try:
        # Extract data directly from the input list of dictionaries
        ids = [item['id'] for item in processed_gems_data]
        documents = [item['document'] for item in processed_gems_data]
        metadatas = [item['metadata'] for item in processed_gems_data]

        # Basic validation after extraction
        if not ids or not documents or not metadatas:
             logging.warning("Extracted lists (ids, documents, metadatas) are empty. Nothing to add.")
             return
        if len(ids) != len(documents) or len(ids) != len(metadatas):
            logging.error(f"Mismatch in lengths after extraction: IDs ({len(ids)}), Documents ({len(documents)}), Metadatas ({len(metadatas)}). Aborting.")
            return

    except KeyError as e:
        logging.error(f"Missing key '{e}' in one of the processed gem data dictionaries. Please ensure 'id', 'document', and 'metadata' keys are present.")
        return
    except Exception as e:
        logging.error(f"Error processing the provided gem data list: {e}")
        return

    logging.info(f"Generating embeddings for {len(documents)} pre-processed gem documents...")
    try:
        embeddings = embedding_model.encode(documents, show_progress_bar=True).tolist()
        logging.info("Embeddings generated.")
    except Exception as e:
        logging.error(f"Failed to generate embeddings for gems: {e}")
        return # Cannot add to DB without embeddings

    logging.info(f"Adding/updating {len(ids)} gems in ChromaDB collection '{collection_name}'...")
    try:
        # Add in batches if necessary, though ChromaDB handles batching internally
        collection.add(
            ids=ids,
            embeddings=embeddings,
            documents=documents,
            metadatas=metadatas
        )
        logging.info(f"Successfully added/updated {len(ids)} gems in ChromaDB collection '{collection_name}'. Processed {len(processed_gems_data)} input entries.")
    except Exception as e:
        logging.error(f"Error adding/updating gems in ChromaDB collection '{collection_name}': {e}")


# --- Passive Skill Loading Logic ---
def load_passives_to_db(client, embedding_model, processed_passives_data: list, collection_name=PASSIVES_COLLECTION_NAME):
    """Loads pre-processed passive skill data (list of dicts) into a ChromaDB collection."""
    if not processed_passives_data:
        logging.warning("No processed passive skill data provided. Skipping passive skill loading.")
        return

    try:
        collection = client.get_or_create_collection(
            name=collection_name,
            metadata={"hnsw:space": "cosine", "embedding_function": "sentence_transformer"}
        )
        logging.info(f"ChromaDB collection '{collection_name}' accessed/created.")
    except Exception as e:
        logging.error(f"Failed to get or create ChromaDB collection '{collection_name}': {e}")
        return

    logging.info(f"Processing {len(processed_passives_data)} pre-processed passive skill entries...")
    try:
        # Extract data directly from the input list of dictionaries
        ids = [item['id'] for item in processed_passives_data]
        documents = [item['document'] for item in processed_passives_data]
        metadatas = [item['metadata'] for item in processed_passives_data]

        # Basic validation after extraction
        if not ids or not documents or not metadatas:
             logging.warning("Extracted lists (ids, documents, metadatas) are empty. Nothing to add.")
             return
        if len(ids) != len(documents) or len(ids) != len(metadatas):
            logging.error(f"Mismatch in lengths after extraction: IDs ({len(ids)}), Documents ({len(documents)}), Metadatas ({len(metadatas)}). Aborting.")
            return

    except KeyError as e:
        logging.error(f"Missing key '{e}' in one of the processed passive data dictionaries. Please ensure 'id', 'document', and 'metadata' keys are present.")
        return
    except Exception as e:
        logging.error(f"Error processing the provided passive skill data list: {e}")
        return

    logging.info(f"Generating embeddings for {len(documents)} pre-processed passive skill documents...")
    try:
        embeddings = embedding_model.encode(documents, show_progress_bar=True).tolist()
        logging.info("Embeddings generated.")
    except Exception as e:
        logging.error(f"Failed to generate embeddings for passive skills: {e}")
        return

    logging.info(f"Adding/updating {len(ids)} passive skills in ChromaDB collection '{collection_name}'...")
    try:
        collection.add(
            ids=ids,
            embeddings=embeddings,
            documents=documents,
            metadatas=metadatas
        )
        logging.info(f"Successfully added/updated {len(ids)} passive skills in ChromaDB collection '{collection_name}'. Processed {len(processed_passives_data)} input entries.")
    except Exception as e:
        logging.error(f"Error adding/updating passive skills in ChromaDB collection '{collection_name}': {e}")