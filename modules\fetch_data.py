import asyncio
import httpx
from bs4 import BeautifulSoup
import re
import math
from collections import defaultdict
import logging
from random_user_agent.user_agent import UserAgent
from random_user_agent.params import SoftwareName, OperatingSystem

# List of User-Agent strings
# Keep existing constants and HEADERS
SKILL_GEM_URL = "https://poe2db.tw/us/Skill_Gems"
SUPPORT_GEM_URL = "https://poe2db.tw/us/Support_Gems"
PASSIVE_SKILL_TREE_URL = "https://poe2db.tw/data/passive-skill-tree/4.2/data_us.json?14"
# HEADERS constant removed, User-Agent is now set per-request

logger = logging.getLogger(__name__)

# Setup User-Agent rotator
software_names = [SoftwareName.CHROME.value]
operating_systems = [OperatingSystem.WINDOWS.value, OperatingSystem.MACOS.value]
user_agent_rotator = UserAgent(software_names=software_names, operating_systems=operating_systems, limit=100)

# Keep existing helper functions: determine_primary_role, generate_description
def determine_primary_role(tags):
    tags = [tag.strip() for tag in tags.split(",") if tag.strip()]
    if "Support" in tags:
        return "Support Modifier"
    if any(tag in ["Attack", "Spell", "Warcry", "Projectile", "AoE"] for tag in tags):
        return "Damage"
    if any(tag in ["Buff", "Aura", "Herald"] for tag in tags):
        return "Utility"
    if any(tag in ["Minion", "Totem"] for tag in tags):
        return "Summon"
    if "Curse" in tags:
        return "Debuff"
    return "Other"

def generate_description(name, tags):
    tags = [tag.strip() for tag in tags.split(",") if tag.strip()]
    desc = ""
    if "Attack" in tags and "Melee" in tags:
        desc += "A melee attack that "
    elif "Warcry" in tags:
        desc += "A warcry that "
    elif "Buff" in tags:
        desc += "A buff that "
    elif "Spell" in tags:
        desc += "A spell that "
    elif "Totem" in tags:
        desc += "Summons a totem that "
    elif "Minion" in tags or "Companion" in tags:
        desc += "Summons a companion that "
    elif "Herald" in tags:
        desc += "A herald effect that "
    else:
        desc += f"Activates {name} to "
    damage_types = [t for t in ["Fire", "Cold", "Lightning", "Physical", "Chaos"] if t in tags]
    if damage_types:
        desc += damage_types[0].lower() + " "
    if "AoE" in tags:
        desc += "area-of-effect "
    if "Strike" in tags:
        desc += "damage, striking enemies"
    elif "Slam" in tags:
        desc += "damage by slamming the ground"
    elif "Projectile" in tags:
        desc += "damage with projectiles"
    elif "Duration" in tags and "Attack" not in tags and "Buff" not in tags and "Herald" not in tags:
        desc += "damage over time"
    elif "Warcry" in tags:
        desc += "boosts combat abilities"
    elif "Buff" in tags or "Herald" in tags:
        desc += "enhances " if "AoE" not in tags else "provides "
    elif "Travel" in tags:
        desc += "moves to the target"
    else:
        desc += "effect"
    if "Channelling" in tags:
        desc += " while channeling"
    if "Travel" in tags and "Channelling" not in tags:
        desc += ", moving to the target"
    if "Persistent" in tags and ("Buff" in tags or "Herald" in tags):
        desc += " persistently"
    if "Duration" in tags and ("Buff" in tags or "Herald" in tags):
        desc += " over time"
    if "Trigger" in tags:
        desc += ", triggered automatically"
    if "Sustained" in tags:
        desc += ", sustained over time"
    if "Payoff" in tags:
        desc += ", with a significant payoff"
    if "Staged" in tags:
        desc += ", gaining stages"
    if "Meta" in tags:
        desc += ", with meta properties"
    if "Conditional" in tags:
        desc += ", under specific conditions"
    if "Companion" in tags:
        desc += ", acting as a companion"
    if "Detonator" in tags:
        desc += ", detonating on command"
    if "Hazard" in tags:
        desc += ", creating a hazard"
    if "Critical" in tags:
        desc += ", with critical strike potential"
    if "Remnant" in tags:
        desc += ", leaving remnants"
    if "Shapeshift" in tags:
        desc += ", allowing shapeshifting"
    if "Ammunition" in tags:
        desc += ", using ammunition"
    if "Grenade" in tags:
        desc += ", launching grenades"
    if "Banner" in tags:
        desc += ", deploying a banner"
    return desc.rstrip(", ") + "."


# Constants for retry logic
MAX_RETRIES = 3
BASE_BACKOFF_DELAY = 1.0  # seconds
RETRYABLE_STATUS_CODES = {408, 429, 500, 502, 503, 504}

async def fetch_with_retry(client: httpx.AsyncClient, url: str, logger: logging.Logger, item_name: str = "Unknown Item", timeout: int = 20) -> httpx.Response | None:
    """
    Fetches a URL with retries on specific errors using exponential backoff.

    Args:
        client: The httpx.AsyncClient instance.
        url: The URL to fetch.
        logger: The logger instance to use for messages.
        item_name: A descriptive name for the item being fetched (for logging).
        timeout: Request timeout in seconds.

    Returns:
        The httpx.Response object on success, or None if all retries fail.
    """
    last_exception = None
    last_status_code = None
    for attempt in range(MAX_RETRIES):
        try:
            logger.debug(f"Attempt {attempt + 1}/{MAX_RETRIES} to fetch {item_name} from {url}")
            # Select a random User-Agent for this specific request
            random_user_agent = user_agent_rotator.get_random_user_agent()
            request_headers = {"User-Agent": random_user_agent}
            logger.debug(f"Using User-Agent: {random_user_agent}")
            # Pass follow_redirects=True, matching the client's configuration, and per-request headers
            response = await client.get(url, headers=request_headers, timeout=timeout, follow_redirects=True)
            response.raise_for_status() # Raise HTTPStatusError for 4xx/5xx responses
            logger.debug(f"Successfully fetched {item_name} from {url} on attempt {attempt + 1}")
            return response # Success

        except httpx.TimeoutException as e:
            logger.warning(f"Timeout fetching {item_name} from {url} (Attempt {attempt + 1}/{MAX_RETRIES}).")
            last_exception = e
            last_status_code = 408 # Treat timeout like a 408 for logging consistency

        except httpx.NetworkError as e:
            # Includes connection errors, DNS errors etc. - generally retryable
            logger.warning(f"Network error fetching {item_name} from {url} (Attempt {attempt + 1}/{MAX_RETRIES}): {e}")
            last_exception = e
            last_status_code = None # No specific status code

        except httpx.HTTPStatusError as e:
            last_exception = e
            last_status_code = e.response.status_code
            if e.response.status_code in RETRYABLE_STATUS_CODES:
                logger.warning(f"HTTP status {e.response.status_code} for {item_name} from {url} (Attempt {attempt + 1}/{MAX_RETRIES}).")
                # Retryable status code, continue to backoff
            else:
                # Non-retryable status code (e.g., 404 Not Found)
                logger.error(f"Non-retryable HTTP status {e.response.status_code} for {item_name} from {url}. Giving up.")
                return None # Indicate permanent failure for non-retryable HTTP errors

        except Exception as e: # Catch any other unexpected errors during the request
             logger.error(f"Unexpected error during fetch attempt {attempt + 1}/{MAX_RETRIES} for {item_name} from {url}: {e}", exc_info=True)
             last_exception = e
             last_status_code = None
             # Decide if truly unexpected errors should be retried; for now, we will retry them.

        # If we are here, an error occurred and it might be retryable
        if attempt < MAX_RETRIES - 1:
            delay = BASE_BACKOFF_DELAY * (2 ** attempt)
            # Add slight jitter? Optional. delay += random.uniform(0, 0.5)
            status_str = f"Status: {last_status_code}" if last_status_code else f"Error: {type(last_exception).__name__}"
            logger.warning(f"Request failed for {item_name} at {url} ({status_str}). Retrying in {delay:.2f}s...")
            await asyncio.sleep(delay)
        else:
            # Max retries reached
            status_str = f"Status: {last_status_code}" if last_status_code else f"Error: {type(last_exception).__name__}"
            logger.error(f"Failed to fetch {item_name} from {url} after {MAX_RETRIES} attempts ({status_str}). Last error: {last_exception}")
            return None # Indicate final failure after all retries

    return None # Should technically be unreachable if loop completes, but safety return


async def fetch_and_process_detail(client, detail_url, name, tags, semaphore):
    """Fetches and parses a single gem's detail page asynchronously."""
    fallback_description = f"Description: Could not fetch details. Using generated description: {generate_description(name, tags)}"
    async with semaphore: # Limit concurrency
        logger.info(f"Acquired semaphore for '{name}', fetching from {detail_url}...")
        # Optional: Add a small delay *within* the semaphore lock if needed
        # await asyncio.sleep(0.1)
        # Use the retry wrapper
        detail_response = await fetch_with_retry(client, detail_url, logger, item_name=name, timeout=20)

        if detail_response is None:
            # Fetch failed permanently after retries
            logger.warning(f"Permanent failure fetching details for {name} from {detail_url}. Using fallback.")
            return fallback_description

        # --- Process successful response ---
        try:
            # No need to call raise_for_status() again, fetch_with_retry handles non-retryable HTTP errors
            detail_soup = BeautifulSoup(detail_response.content, "html.parser")
            logger.info(f"Successfully fetched and parsed details for '{name}'.")

            # Extract ALL description blocks
            desc_elems = detail_soup.find_all("div", class_="secDescrText")
            scraped_description_parts = [elem.get_text(separator="\n", strip=True) for elem in desc_elems]
            # Join multiple descriptions with a separator for clarity
            scraped_description = "\n---\n".join(scraped_description_parts) if scraped_description_parts else "No description found."

            # Extract hybrid properties
            hybrid_prop_elems = detail_soup.find_all("div", class_="hybridProperty")
            hybrid_props = [prop.get_text(strip=True) for prop in hybrid_prop_elems]

            # Extract explicit mods (logic remains the same)
            mod_elems = detail_soup.find_all("div", class_="explicitMod")
            explicit_mods = [mod.get_text(strip=True) for mod in mod_elems]

            # Combine all extracted data
            detailed_description = f"Description(s):\n{scraped_description}" # Use the potentially multi-part description
            if hybrid_props:
                detailed_description += "\n\nStats / Properties:\n- " + "\n- ".join(hybrid_props)
            if explicit_mods:
                detailed_description += "\n\nExplicit Mods:\n- " + "\n- ".join(explicit_mods)
            logger.info(f"Successfully extracted description and mods for '{name}'.")
            return detailed_description

        # Catch parsing errors or other unexpected issues after successful fetch
        except Exception as e:
             logger.error(f"Error processing successfully fetched details for {name} from {detail_url}: {e}", exc_info=True)
            # Still return fallback if processing fails
             return fallback_description


async def fetch_gem_data(url, gem_type="Skill", max_concurrent=10):
    """Fetches gem summary and detail data asynchronously."""
    gem_list = []
    tasks = []
    # Use httpx.AsyncClient for async requests
    # follow_redirects=True is important for some sites
    # Removed headers=HEADERS from client initialization
    async with httpx.AsyncClient(follow_redirects=True, timeout=30) as client:
        try:
            logger.info(f"Fetching initial {gem_type} list from {url}...")
            response = await client.get(url)
            response.raise_for_status()
            logger.info(f"Successfully fetched initial {gem_type} list.")
        except httpx.RequestError as e:
            logger.error(f"Failed to fetch {gem_type} gem list from {url}: {e}")
            return []
        except Exception as e:
            logger.error(f"An unexpected error occurred fetching the {gem_type} list from {url}: {e}", exc_info=True)
            return []

        soup = BeautifulSoup(response.content, "html.parser")
        gem_tables = soup.select("table.table-hover.table-striped.filters")
        if not gem_tables:
            logger.error(f"No gem tables found for {gem_type} at {url}")
            return []

        semaphore = asyncio.Semaphore(max_concurrent) # Rate limiting

        for table in gem_tables:
            gem_rows = table.select("tbody tr")
            logger.info(f"Processing {len(gem_rows)} rows from a {gem_type} table...")
            for row in gem_rows:
                cols = row.find_all("td")
                if len(cols) < 2:
                    continue
                name_col = cols[1]
                name_link = name_col.find("a")
                if not name_link:
                    continue

                # Extract name cleanly (handling potential level in brackets)
                name_text = ""
                for content in name_col.contents:
                    if content.name == "div" and "gem_tags" in content.get("class", []):
                        break
                    if isinstance(content, str):
                        name_text += content
                    elif content.name == "a":
                        name_text += content.text
                name_text = name_text.strip()
                match = re.match(r"^(.*?)\s*\((\d+)\)$", name_text)
                if match:
                    name = match.group(1).strip()
                    level = match.group(2)
                else:
                    name = name_text
                    level = "1" # Default level if not specified

                tags_elem = name_col.find("div", class_="gem_tags")
                if not tags_elem:
                    logger.warning(f"No tags found for gem: {name}")
                    tags_str = "" # Assign empty string if no tags
                else:
                    tags = [span.text.strip() for span in tags_elem.find_all("span")]
                    tags_str = ", ".join(tags)

                relative_url = name_link.get('href')
                if relative_url:
                    detail_url = "https://poe2db.tw" + relative_url
                    # Create a task to fetch details concurrently
                    task = asyncio.create_task(
                        fetch_and_process_detail(client, detail_url, name, tags_str, semaphore)
                    )
                    # Store task and basic info to reconstruct the gem dict later
                    tasks.append({"task": task, "name": name, "level": level, "tags": tags_str, "type": gem_type})
                else:
                     logger.warning(f"No detail URL found for gem: {name}")
                     # Add gem with fallback description directly if no URL
                     primary_role = determine_primary_role(tags_str)
                     fallback_desc = f"Description: No detail URL found. Using generated description: {generate_description(name, tags_str)}"
                     gem_list.append({
                         "name": name,
                         "level": level,
                         "tags": tags_str,
                         "description": fallback_desc,
                         "type": gem_type,
                         "primary_role": primary_role
                     })

            logger.info(f"Finished queuing tasks for one {gem_type} gem table.")

        # Wait for all detail fetching tasks to complete
        logger.info(f"Waiting for {len(tasks)} detail fetching tasks to complete...")
        detail_results = await asyncio.gather(*(t["task"] for t in tasks), return_exceptions=True)
        logger.info("All detail fetching tasks finished.")

        # Process results
        for i, result in enumerate(detail_results):
            task_info = tasks[i]
            description = ""
            if isinstance(result, Exception):
                logger.error(f"Task for {task_info['name']} failed: {result}", exc_info=result)
                # Generate fallback description if task failed entirely
                description = f"Description: Fetching failed ({type(result).__name__}). Using generated description: {generate_description(task_info['name'], task_info['tags'])}"
            elif result: # Check if result is not None or empty
                description = result
            else:
                # Handle cases where fetch_and_process_detail might return None (though it shouldn't with current logic)
                 logger.warning(f"Task for {task_info['name']} returned an unexpected empty result.")
                 description = f"Description: Fetching returned empty result. Using generated description: {generate_description(task_info['name'], task_info['tags'])}"


            primary_role = determine_primary_role(task_info['tags'])
            gem_list.append({
                "name": task_info['name'],
                "level": task_info['level'],
                "tags": task_info['tags'],
                "description": description,
                "type": task_info['type'],
                "primary_role": primary_role
            })

    logger.info(f"Completed fetching and processing for {gem_type}. Total gems: {len(gem_list)}")
    return gem_list


# --- Keep existing passive skill tree functions ---
# (get_orbit_angles, set_nodes_xy, fetch_passive_skill_data)
# Note: fetch_passive_skill_data still uses synchronous requests.
# If this also needs to be async, it would require similar refactoring.

def get_orbit_angles(skills_per_orbit):
    orbit_angles = {}
    for orbit, count in enumerate(skills_per_orbit):
        angles = []
        if count == 16:
            angles = [0, 30, 45, 60, 90, 120, 135, 150, 180, 210, 225, 240, 270, 300, 315, 330]
        elif count == 40:
            angles = [0, 10, 20, 30, 40, 45, 50, 60, 70, 80, 90, 100, 110, 120, 130, 135, 140, 150, 160, 170, 180, 190, 200, 210, 220, 225, 230, 240, 250, 260, 270, 280, 290, 300, 310, 315, 320, 330, 340, 350]
        else:
            for i in range(count):
                angles.append(360 * i / count)
        orbit_angles[orbit] = [angle * math.pi / 180 for angle in angles]
    return orbit_angles

def set_nodes_xy(passive_data):
    orbit_angles = get_orbit_angles(passive_data["constants"]["skillsPerOrbit"])
    for group_id, group in passive_data["groups"].items():
        for node_id in group.get("nodes", []):
            node = passive_data["nodes"].get(node_id)
            if not node:
                continue
            if "orbit" in node and "orbitIndex" in node:
                angle = orbit_angles[node["orbit"]][node["orbitIndex"]]
                radius = passive_data["constants"]["orbitRadii"][node["orbit"]]
                node["x"] = round(group["x"] + radius * math.sin(angle))
                node["y"] = round(group["y"] - radius * math.cos(angle))
            else:
                node["x"] = group["x"]
                node["y"] = group["y"]

# This function remains synchronous for now
def fetch_passive_skill_data(url):
    try:
        # Using httpx synchronously here for consistency, but could remain requests
        # Removed headers=HEADERS from client initialization
        with httpx.Client(follow_redirects=True, timeout=30) as client:
            response = client.get(url)
            response.raise_for_status()
    except httpx.RequestError as e:
        print(f"Failed to fetch passive skill tree data from {url}: {e}")
        return None, None, None, None, None # Added None for passive_data
    except Exception as e:
        print(f"An unexpected error occurred fetching passive skill tree data from {url}: {e}")
        return None, None, None, None, None # Added None for passive_data

    try:
        passive_data = response.json()
    except Exception as e: # Catch JSON decoding errors specifically
        print(f"Failed to decode JSON from passive skill tree data at {url}: {e}")
        return None, None, None, None, None

    set_nodes_xy(passive_data)
    classes = {cls["name"]: None for cls in passive_data["classes"]}
    class_start_nodes = {}
    for idx, cls in enumerate(passive_data["classes"]):
        class_name = cls["name"]
        for node_id, node in passive_data["nodes"].items():
            if "classStartIndex" in node:
                if isinstance(node.get("classStartIndex"), list): # Check if it's a list
                    if idx in node["classStartIndex"]:
                        class_start_nodes[class_name] = node_id
                        break
                elif node.get("classStartIndex") == idx: # Check if it's an integer and matches
                    class_start_nodes[class_name] = node_id
                    break

    nodes = {}
    connections = defaultdict(list)
    for node_id, node in passive_data["nodes"].items():
        if node_id == "root":
            continue
        # Ensure default values are provided safely using .get()
        stats = ", ".join(node.get("stats", [])) if node.get("stats") else "None"
        x = node.get("x", 0)
        y = node.get("y", 0)
        group = node.get("group", "None") # Keep as string "None" if missing
        is_notable = node.get("isNotable", False)
        is_keystone = node.get("isKeystone", False)
        is_mastery = node.get("isMastery", False)
        # Handle classStartIndex potentially being missing or not an int/list
        class_start_index_raw = node.get("classStartIndex")
        class_start_index = str(class_start_index_raw) if class_start_index_raw is not None else "None"
        ascendancy_name = node.get("ascendancyName", "None") # Keep as string "None" if missing

        nodes[node_id] = {
            "name": node.get("name", "Unknown"),
            "stats": stats,
            "x": x,
            "y": y,
            "group": group,
            "is_notable": is_notable,
            "is_keystone": is_keystone,
            "is_mastery": is_mastery,
            "class_start_index": class_start_index,
            "ascendancy_name": ascendancy_name
        }
        # Safely handle 'out' and 'in' keys which might be missing
        for out_id in node.get("out", []):
            connections[node_id].append(out_id)
        for in_id in node.get("in", []):
            # Ensure in_id exists in nodes before adding connection
            if in_id in passive_data["nodes"]:
                 connections[in_id].append(node_id)

    passive_list = [
        (
            node_id,
            node["name"],
            node["stats"],
            str(node["x"]),
            str(node["y"]),
            node["group"],
            str(node["is_notable"]),
            str(node["is_keystone"]),
            str(node["is_mastery"]),
            node["class_start_index"],
            node["ascendancy_name"]
        )
        for node_id, node in nodes.items()
    ]
    # Return passive_data as well
    return passive_list, nodes, connections, class_start_nodes, passive_data