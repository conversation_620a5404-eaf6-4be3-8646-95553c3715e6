import json
import chromadb
import os
import logging
from sentence_transformers import SentenceTransformer
from chromadb.utils import embedding_functions

# Import data fetching functions for gems and passives
from modules.fetch_data import fetch_gem_data, fetch_passive_skill_data, SKILL_GEM_URL, SUPPORT_GEM_URL, PASSIVE_SKILL_TREE_URL

# --- Default Configuration ---
DEFAULT_DB_PATH = "data/chroma_db/"
DEFAULT_EMBEDDING_MODEL = 'all-MiniLM-L6-v2'
DEFAULT_BATCH_SIZE = 100 # Unified batch size, adjust if needed

# Item specific defaults
DEFAULT_ITEMS_RAW_DATA_FILE = "data/poe2_items_raw.json"
DEFAULT_ITEMS_COLLECTION_NAME = "poe2_items"

# Gem specific defaults
DEFAULT_GEMS_COLLECTION_NAME = "poe2_gems"

# Passive specific defaults
DEFAULT_PASSIVES_COLLECTION_NAME = "poe2_passive_nodes"
DEFAULT_CONNECTIONS_FILE = "data/connections_data.json"

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- ChromaDB Generic Helper Functions ---

def _initialize_chroma_client(db_path):
    """Initializes and returns a persistent ChromaDB client."""
    logging.info(f"Initializing ChromaDB client at path: {db_path}")
    try:
        os.makedirs(db_path, exist_ok=True)
        client = chromadb.PersistentClient(path=db_path)
        return client
    except Exception as e:
        logging.error(f"Failed to initialize ChromaDB client: {e}")
        raise

def _get_or_create_collection(client, collection_name, embedding_model_name):
    """Gets or creates a ChromaDB collection with the specified embedding function."""
    logging.info(f"Getting or creating collection: {collection_name}")
    try:
        sentence_transformer_ef = embedding_functions.SentenceTransformerEmbeddingFunction(model_name=embedding_model_name)
        collection = client.get_or_create_collection(
            name=collection_name,
            embedding_function=sentence_transformer_ef
        )
        logging.info(f"Using embedding model: {embedding_model_name} for collection '{collection_name}'")
        return collection
    except Exception as e:
        logging.error(f"Failed to get or create collection '{collection_name}': {e}")
        raise

def _batch_upsert(collection, ids, documents, metadatas, batch_size):
    """Upserts data into the collection in batches."""
    collection_name = collection.name
    if not ids:
        logging.warning(f"No items to upsert for collection '{collection_name}'. Skipping.")
        return

    logging.info(f"Upserting {len(ids)} items into the '{collection_name}' collection...")
    num_batches = (len(ids) + batch_size - 1) // batch_size

    for i in range(num_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, len(ids))
        batch_ids = ids[start_idx:end_idx]
        batch_documents = documents[start_idx:end_idx]
        batch_metadatas = metadatas[start_idx:end_idx]

        logging.info(f"Processing batch {i+1}/{num_batches} for '{collection_name}' ({len(batch_ids)} items)")
        try:
            collection.upsert(
                ids=batch_ids,
                documents=batch_documents,
                metadatas=batch_metadatas
            )
            logging.debug(f"Successfully upserted batch {i+1} for '{collection_name}'.")
        except Exception as e:
            logging.error(f"Error upserting batch {i+1} to collection '{collection_name}': {e}")
            continue # Log error and continue

    logging.info(f"Finished upserting {len(ids)} items into '{collection_name}' in {num_batches} batches.")
    # Verification
    try:
        count = collection.count()
        logging.info(f"Verification: Collection '{collection_name}' now contains {count} items.")
    except Exception as e:
        logging.error(f"Error during count verification for '{collection_name}': {e}")

# --- Item Specific Helper Functions ---

def create_item_document_text(item):
    """Creates a text representation for item embedding, prioritizing detailed description."""
    name = item.get('name', 'Unknown Item')
    base_type = item.get('base_type', 'Unknown Type')
    detailed_description = item.get('detailed_description') # Get the new field

    # If detailed_description exists and is not a placeholder/error message, use it.
    if detailed_description and "Not fetched" not in detailed_description and "Failed to fetch" not in detailed_description and "No detail page link" not in detailed_description:
        # Prepend Name and Base Type for context, then add the detailed info
        return f"Item: {name}\nBase Type: {base_type}\n{detailed_description}"
    else:
        # Fallback to original method if detailed description is missing or failed
        logging.debug(f"Using fallback document generation for item: {name}")
        implicits = ", ".join(item.get('implicit_stats', []))
        explicits = ", ".join(item.get('explicit_stats', []))
        doc_parts = [f"Item: {name}", f"Type: {base_type}"]
        if implicits:
            doc_parts.append(f"Implicits: {implicits}")
        if explicits:
            doc_parts.append(f"Stats: {explicits}")
        # Add the placeholder/error message if it exists
        if detailed_description:
             doc_parts.append(f"Details: {detailed_description}")
        return "\n".join(doc_parts) # Use newline for better readability in fallback too

def create_item_metadata(item):
    """Creates a metadata dictionary for the item."""
    metadata = {}
    for key, value in item.items():
        if isinstance(value, list):
            metadata[key] = ", ".join(map(str, value))
        elif value is not None:
            metadata[key] = value

    # Heuristic tagging for item type
    name = item.get("name", "")
    tags = item.get("tags", [])
    # Normalize tags to list
    if isinstance(tags, str):
        tags = [tags]
    elif not isinstance(tags, list):
        tags = []

    # Minimum requirement: tag all crossbows
    if "Crossbow" in name or any("Crossbow" in str(tag) for tag in tags):
        metadata["type"] = "crossbow"
    # Future: add more heuristics for other types here

    return metadata

# --- Gem Specific Helper Functions ---

def create_gem_document_text(gem):
    """Creates the document text for a gem based on the design."""
    name = gem.get('name', 'Unknown Gem')
    description = gem.get('description', '')
    tags = gem.get('tags', '')
    return f"{name}: {description} Tags: {tags}"

def create_gem_metadata(gem):
    """Creates the metadata dictionary for a gem based on the design."""
    return {
        "name": gem.get('name'),
        "type": gem.get('type'),
        "level": gem.get('level'),
        "tags": gem.get('tags'),
        "primary_role": gem.get('primary_role')
    }

# --- Passive Specific Helper Functions ---

def create_passive_document_text(node_data):
    """Creates the document text for a passive node based on the design."""
    name = node_data.get('name', 'Unknown Node')
    stats = node_data.get('stats', 'No Stats')
    return f"{name}: {stats}"

def create_passive_metadata(node_id, node_data):
    """Creates the metadata dictionary for a passive node based on the design."""
    return {
        "node_id": node_id,
        "name": node_data.get('name'),
        "stats": node_data.get('stats'),
        "x": node_data.get('x'),
        "y": node_data.get('y'),
        "group": node_data.get('group'),
        "is_notable": node_data.get('is_notable', False),
        "is_keystone": node_data.get('is_keystone', False),
        "is_mastery": node_data.get('is_mastery', False),
        "class_start_index": node_data.get('class_start_index'),
        "ascendancy_name": node_data.get('ascendancy_name'),
        "type": "passive"
    }

# --- Main Loading Functions ---

def load_items_to_db(
    client, # Expects an initialized client
    raw_data_file=DEFAULT_ITEMS_RAW_DATA_FILE,
    collection_name=DEFAULT_ITEMS_COLLECTION_NAME,
    embedding_model_name=DEFAULT_EMBEDDING_MODEL,
    batch_size=DEFAULT_BATCH_SIZE
):
    """Loads item data from a JSON file into a ChromaDB collection."""
    logging.info(f"--- Starting Item Loading for collection '{collection_name}' ---")

    # 1. Load Raw Data
    logging.info(f"Loading raw item data from: {raw_data_file}")
    try:
        with open(raw_data_file, 'r', encoding='utf-8') as f:
            raw_items = json.load(f)
        logging.info(f"Successfully loaded {len(raw_items)} items.")
    except FileNotFoundError:
        logging.error(f"Error: Raw data file not found at {raw_data_file}. Skipping item loading.")
        return
    except json.JSONDecodeError:
        logging.error(f"Error: Could not decode JSON from {raw_data_file}. Skipping item loading.")
        return
    except Exception as e:
        logging.error(f"An unexpected error occurred loading item data: {e}. Skipping item loading.")
        return

    if not raw_items:
        logging.warning("No items found in the raw data file. Skipping item loading.")
        return

    # 2. Process Data
    logging.info("Processing item data for ChromaDB...")
    documents = []
    metadatas = []
    ids = []
    processed_ids = set()

    for i, item in enumerate(raw_items):
        item_name = item.get('name')
        if not item_name:
            logging.warning(f"Skipping item at index {i} due to missing name.")
            continue

        item_id = item_name
        counter = 1
        while item_id in processed_ids:
            item_id = f"{item_name}_{counter}"
            counter += 1
        processed_ids.add(item_id)
        ids.append(item_id)

        documents.append(create_item_document_text(item)) # Use renamed helper
        metadatas.append(create_item_metadata(item))     # Use renamed helper

    logging.info(f"Processed {len(ids)} items for ChromaDB.")

    # 3. Get Collection
    collection = _get_or_create_collection(client, collection_name, embedding_model_name)

    # 4. Upsert Data
    _batch_upsert(collection, ids, documents, metadatas, batch_size)

    logging.info(f"--- Finished Item Loading for collection '{collection_name}' ---")


def load_gems_to_db(
    client, # Expects an initialized client
    collection_name=DEFAULT_GEMS_COLLECTION_NAME,
    embedding_model_name=DEFAULT_EMBEDDING_MODEL,
    batch_size=DEFAULT_BATCH_SIZE
):
    """Fetches gem data and loads it into the specified ChromaDB collection."""
    logging.info(f"--- Starting Gem Loading for collection '{collection_name}' ---")

    # 1. Fetch Data
    logging.info("Fetching Skill gem data...")
    try:
        skill_gems = fetch_gem_data(SKILL_GEM_URL, gem_type="Skill")
        logging.info(f"Fetched {len(skill_gems)} Skill gems.")
        logging.info("Fetching Support gem data...")
        support_gems = fetch_gem_data(SUPPORT_GEM_URL, gem_type="Support")
        logging.info(f"Fetched {len(support_gems)} Support gems.")
    except Exception as e:
        logging.error(f"Failed to fetch gem data: {e}. Skipping gem loading.")
        return

    all_gems = skill_gems + support_gems
    if not all_gems:
        logging.warning("No gem data fetched. Skipping gem loading.")
        return

    logging.info(f"Total gems fetched: {len(all_gems)}")

    # 2. Process Data
    logging.info("Processing gem data for ChromaDB...")
    documents = []
    metadatas = []
    ids = []
    processed_names = set()

    for i, gem in enumerate(all_gems):
        gem_name = gem.get('name')
        if not gem_name:
            logging.warning(f"Skipping gem at index {i} due to missing name.")
            continue

        gem_id = gem_name
        counter = 1
        while gem_id in processed_names:
            logging.warning(f"Duplicate gem name found: '{gem_name}'. Appending counter.")
            gem_id = f"{gem_name}_{counter}"
            counter += 1
        processed_names.add(gem_id)
        ids.append(gem_id)

        documents.append(create_gem_document_text(gem))
        metadatas.append(create_gem_metadata(gem))

    logging.info(f"Processed {len(ids)} gems for ChromaDB.")

    # 3. Get Collection
    collection = _get_or_create_collection(client, collection_name, embedding_model_name)

    # 4. Upsert Data
    _batch_upsert(collection, ids, documents, metadatas, batch_size)

    logging.info(f"--- Finished Gem Loading for collection '{collection_name}' ---")


def load_passives_to_db(
    client, # Expects an initialized client
    collection_name=DEFAULT_PASSIVES_COLLECTION_NAME,
    connections_file=DEFAULT_CONNECTIONS_FILE,
    embedding_model_name=DEFAULT_EMBEDDING_MODEL,
    batch_size=DEFAULT_BATCH_SIZE
):
    """Fetches passive skill tree data and loads nodes into ChromaDB and connections to JSON."""
    logging.info(f"--- Starting Passive Skill Loading for collection '{collection_name}' ---")

    # 1. Fetch Data
    logging.info("Fetching passive skill tree data...")
    try:
        # fetch_passive_skill_data returns: passive_list, nodes, connections, class_start_nodes, passive_data
        _, nodes_data, connections_data, _, _ = fetch_passive_skill_data(PASSIVE_SKILL_TREE_URL)
    except Exception as e:
        logging.error(f"Failed to fetch passive skill data: {e}. Skipping passive loading.")
        return

    if not nodes_data:
        logging.warning("No passive node data fetched. Skipping passive loading.")
        return
    if not connections_data:
        logging.warning("No passive connection data fetched, but proceeding with nodes.")

    logging.info(f"Fetched {len(nodes_data)} passive nodes and connection data.")

    # 2. Process Node Data for ChromaDB
    logging.info("Processing passive node data for ChromaDB...")
    documents = []
    metadatas = []
    ids = []

    for node_id, node_details in nodes_data.items():
        if not node_id or node_id == "root":
             logging.debug(f"Skipping node with ID: {node_id}")
             continue

        ids.append(str(node_id))
        documents.append(create_passive_document_text(node_details))
        metadatas.append(create_passive_metadata(node_id, node_details))

    logging.info(f"Processed {len(ids)} passive nodes for ChromaDB.")

    # 3. Get Collection
    collection = _get_or_create_collection(client, collection_name, embedding_model_name)

    # 4. Upsert Node Data
    _batch_upsert(collection, ids, documents, metadatas, batch_size)

    # 5. Save Connection Data
    if connections_data:
        logging.info(f"Saving passive connections data to: {connections_file}")
        try:
            os.makedirs(os.path.dirname(connections_file), exist_ok=True)
            with open(connections_file, 'w', encoding='utf-8') as f:
                json.dump(dict(connections_data), f, indent=4)
            logging.info("Successfully saved connections data.")
        except IOError as e:
            logging.error(f"Error saving connections data to {connections_file}: {e}")
        except Exception as e:
            logging.error(f"An unexpected error occurred saving connections data: {e}")

    logging.info(f"--- Finished Passive Skill Loading for collection '{collection_name}' ---")


# --- Main Execution (for standalone use) ---
if __name__ == "__main__":
    logging.info("Running ChromaDB loading script as standalone...")

    # Check for required libraries first
    try:
        import chromadb
        import sentence_transformers
        import requests # Needed for fetch_data
        import bs4      # Needed for fetch_data (BeautifulSoup)
    except ImportError as e:
        print("---------------------------------------------------------", flush=True)
        print(f"Error: Missing required library -> {e.name}", flush=True)
        print("Please ensure 'chromadb', 'sentence-transformers', 'requests', and 'beautifulsoup4' are installed.", flush=True)
        print("You can install them using: pip install chromadb sentence-transformers requests beautifulsoup4", flush=True)
        print("---------------------------------------------------------", flush=True)
        exit(1)

    chroma_client = None
    try:
        # Initialize client once
        chroma_client = _initialize_chroma_client(DEFAULT_DB_PATH)

        # Load Items
        load_items_to_db(
            client=chroma_client,
            raw_data_file=DEFAULT_ITEMS_RAW_DATA_FILE,
            collection_name=DEFAULT_ITEMS_COLLECTION_NAME,
            embedding_model_name=DEFAULT_EMBEDDING_MODEL,
            batch_size=DEFAULT_BATCH_SIZE # Use unified batch size
        )

        # Load Gems
        load_gems_to_db(
            client=chroma_client,
            collection_name=DEFAULT_GEMS_COLLECTION_NAME,
            embedding_model_name=DEFAULT_EMBEDDING_MODEL,
            batch_size=DEFAULT_BATCH_SIZE
        )

        # Load Passives
        load_passives_to_db(
            client=chroma_client,
            collection_name=DEFAULT_PASSIVES_COLLECTION_NAME,
            connections_file=DEFAULT_CONNECTIONS_FILE,
            embedding_model_name=DEFAULT_EMBEDDING_MODEL,
            batch_size=DEFAULT_BATCH_SIZE
        )

    except Exception as e:
        logging.error(f"An critical error occurred during the main execution: {e}")
        # This could be client initialization or collection creation failure

    finally:
        logging.info("Standalone script execution finished.")
        # PersistentClient doesn't require explicit closing