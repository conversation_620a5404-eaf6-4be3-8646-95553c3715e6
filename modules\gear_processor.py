from PIL import Image
import pytesseract
import re
import json
import os

def process_gear_image(image_path):
    """
    Processes a PoE2 gear image to extract basic item information using OCR.

    Args:
        image_path (str): Path to the uploaded image file.

    Returns:
        dict: Dictionary containing extracted item information, or an error flag.
    """
    result = {
        "name": None,
        "type": None,
        "rarity": None,
        "modifiers": [],
        "error": False,
        "error_msg": ""
    }

    # Step 1: Open image and perform OCR
    try:
        img = Image.open(image_path)
    except Exception as e:
        result["error"] = True
        result["error_msg"] = f"Failed to open image: {e}"
        return result

    try:
        ocr_text = pytesseract.image_to_string(img)
    except pytesseract.TesseractNotFoundError:
        result["error"] = True
        result["error_msg"] = "Tesseract OCR engine not found."
        return result
    except Exception as e:
        result["error"] = True
        result["error_msg"] = f"OCR failed: {e}"
        return result

    # Step 2: Basic parsing logic
    # Split text into lines and remove empty lines/whitespace
    lines = [line.strip() for line in ocr_text.splitlines() if line.strip()]
    if not lines or len(lines) < 2:
        result["error"] = True
        result["error_msg"] = "OCR did not return enough text to parse."
        return result

    # Heuristic: First line is usually the item name, second is type or rarity
    result["name"] = lines[0]

    # Try to find rarity in the first few lines
    rarity_keywords = ["Normal", "Magic", "Rare", "Unique"]
    result["rarity"] = None
    for line in lines[:4]:
        for rarity in rarity_keywords:
            if rarity.lower() in line.lower():
                result["rarity"] = rarity
                break
        if result["rarity"]:
            break

    # Type: Try to find a line that is not name or rarity, often second or third line
    # If rarity is found, type is likely the line after rarity
    type_line = None
    if result["rarity"]:
        for idx, line in enumerate(lines[:6]):
            if result["rarity"].lower() in line.lower():
                # Type is likely the next line if it exists and is not empty
                if idx + 1 < len(lines):
                    type_line = lines[idx + 1]
                break
    if not type_line and len(lines) > 1:
        # Fallback: second line
        type_line = lines[1]
    result["type"] = type_line

    # Modifiers: lines starting with '+', '%', 'Adds', 'Increased', or containing numbers and keywords
    mod_pattern = re.compile(r"^(?:\+|%|Adds|Increased|Reduced|Gain|Grants|[0-9])", re.IGNORECASE)
    mods = []
    for line in lines:
        if mod_pattern.match(line):
            mods.append(line)
        # Also include lines with typical mod keywords
        elif any(kw in line for kw in ["Increased", "Reduced", "Adds", "to", "Gain", "Grants"]):
            mods.append(line)
    # Remove duplicates and lines that are just numbers
    mods = [m for m in set(mods) if not re.fullmatch(r"\d+", m)]
    result["modifiers"] = mods

    # Remove error fields if no error
    if not result["error"]:
        result.pop("error")
        result.pop("error_msg")

    return result