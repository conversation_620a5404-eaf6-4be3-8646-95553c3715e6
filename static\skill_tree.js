// static/skill_tree.js

// --- Initialization (Data will be provided by HTML) ---
// These will be populated by the script block in the HTML
let nodes = {};
let connections = [];
let path = [];
let constants = {};
let initial_center_x = 0;
let initial_center_y = 0;
let tree_width = 0;
let tree_height = 0;

// --- DOM Elements ---
let canvas = null;
let ctx = null;
let tooltip = null;
let statsDisplay = null;
let userQueryInput = null;
let buildGoalInput = null;
let getSuggestionButton = null;
let askGeneralQuestionButton = null;
let llmResponseOutput = null;
let llmThoughtsContainer = null;
let toggleThoughtsButton = null;
let llmThoughtsOutput = null;
let saveButton = null;

let imageUploadInput = null;
let uploadedFileNameSpan = null;
let getSuggestionWithImageButton = null;
let nodeSearchInput = null;
let nodeSearchButton = null;
// --- Canvas State ---
let zoomLevel = 1;
let centerX = 0;
let centerY = 0;
let isDragging = false;
let lastX, lastY;
const MIN_ZOOM = 0.1;
const MAX_ZOOM = 15;
const ZOOM_FACTOR = 0.05;

const nodeSizes = {
    normal: 40,
    notable: 56,
    keystone: 104
};

// --- User Interaction State ---
const clickedNodes = [];
const selectedPath = new Set();
const selectedNodes = new Set(); // Use Set for efficient add/delete/has checks
const selectedNodeChoices = new Map(); // Stores { nodeId: 'Strength' | 'Dexterity' | 'Intelligence' }

let chatHistory = []; // Stores the conversation history for general chat
let uploadedImageFilenames = []; // Stores filenames after successful uploads
let searchedNodeId = null; // Stores the ID of the node found by search for highlighting
// --- Helper Functions ---
function getConnectId(node1Id, node2Id) {
    let s = parseInt(node1Id);
    let n = parseInt(node2Id);
    if (s > n) [s, n] = [n, s];
    return `c${s}-${n}`;
}

function shortestPath(startId, targetId) {
    const distances = {};
    const previous = {};
    const queue = [startId];
    const visited = new Set();

    for (const nodeId in nodes) {
        distances[nodeId] = Infinity;
    }
    distances[startId] = 0;

    while (queue.length > 0) {
        // Basic priority queue simulation (can be optimized)
        queue.sort((a, b) => distances[a] - distances[b]);
        const current = queue.shift();

        if (visited.has(current)) continue;
        visited.add(current);

        if (current === targetId) {
            const pathResult = [];
            let node = targetId;
            while (node != null) {
                pathResult.push(node);
                node = previous[node];
            }
            return pathResult.reverse();
        }

        const neighbors = connections
            .filter(conn => conn.from === current || conn.to === current)
            .map(conn => conn.from === current ? conn.to : conn.from)
            .filter(neighborId => nodes[neighborId]); // Ensure neighbor exists

        for (const neighbor of neighbors) {
            if (visited.has(neighbor)) continue;
            const newDist = distances[current] + 1; // Assuming edge weight is 1
            if (newDist < distances[neighbor]) {
                distances[neighbor] = newDist;
                previous[neighbor] = current;
                if (!queue.includes(neighbor)) { // Add only if not already queued
                   queue.push(neighbor);
                }
            }
        }
    }
    return []; // No path found
}


function updatePath() {
    selectedPath.clear();
    // Rebuild path based on selectedNodes order (simple connection for now)
    // A more robust approach might find the minimal spanning tree or shortest path covering all selected nodes
    // For now, just highlight selected nodes
    selectedNodes.forEach(nodeId => selectedPath.add(nodeId)); // Simple highlight

    // If connecting in order is desired:
    /*
    if (clickedNodes.length > 1) {
        for (let i = 0; i < clickedNodes.length - 1; i++) {
            const start = clickedNodes[i];
            const target = clickedNodes[i + 1];
            // Need a graph representation or access to connections to find neighbors
            // This part needs refinement based on how connections are structured
            // Assuming shortestPath works:
             const pathSegment = shortestPath(start, target);
             pathSegment.forEach(nodeId => selectedPath.add(nodeId));
        }
    } else if (clickedNodes.length === 1) {
         selectedPath.add(clickedNodes[0]);
    }
    */

    calculateStats();
    draw();
}

let previewNodeId = null; // Node ID for stat preview
let previewPath = new Set(); // Node IDs for shortest path preview

function calculateStats(previewId = null) {
    const statMap = {};
    if (!statMap["Strength"]) statMap["Strength"] = 0;
    if (!statMap["Dexterity"]) statMap["Dexterity"] = 0;
    if (!statMap["Intelligence"]) statMap["Intelligence"] = 0;
    statMap["Strength_type"] = "flat";
    statMap["Dexterity_type"] = "flat";
    statMap["Intelligence_type"] = "flat";

    function addNodeStats(nodeId) {
        const node = nodes[nodeId];
        if (!node || !node.stats) return;

        if (selectedNodeChoices.has(nodeId)) {
            const chosenAttribute = selectedNodeChoices.get(nodeId);
            if (chosenAttribute === 'Strength' || chosenAttribute === 'Dexterity' || chosenAttribute === 'Intelligence') {
                if (!statMap[chosenAttribute]) statMap[chosenAttribute] = 0;
                statMap[chosenAttribute] += 5;
                statMap[chosenAttribute + "_type"] = "flat";
            }
            return;
        }

        const stats = node.stats.split(", ");
        stats.forEach(stat => {
            if (!stat || stat === "None") return;
            if (stat === "+5 to Strength, Dexterity or Intelligence") return;

            let match = stat.match(/(\d+)% increased (.+)/);
            if (match) {
                const value = parseInt(match[1]);
                const statName = match[2];
                if (!statMap[statName]) statMap[statName] = 0;
                statMap[statName] += value;
                statMap[statName + "_type"] = "percent";
                return;
            }
            match = stat.match(/\+(\d+) to (.+)/);
            if (match) {
                const value = parseInt(match[1]);
                const statName = match[2];
                if (statName === "Strength" || statName === "Dexterity" || statName === "Intelligence") {
                    if (!statMap[statName]) statMap[statName] = 0;
                    statMap[statName] += value;
                    statMap[statName + "_type"] = "flat";
                } else {
                    if (!statMap[statName]) statMap[statName] = 0;
                    statMap[statName] += value;
                    statMap[statName + "_type"] = "flat";
                }
                return;
            }
            match = stat.match(/(\d+)%? (.+)/);
            if (match) {
                const value = parseInt(match[1]);
                const statName = match[2];
                if (!statMap[statName]) statMap[statName] = 0;
                statMap[statName] += value;
                statMap[statName + "_type"] = stat.includes('%') ? "percent" : "flat";
                return;
            }
            if (!statMap[stat]) statMap[stat] = 0;
            statMap[stat] += 1;
            statMap[stat + "_type"] = "count";
        });
    }

    selectedNodes.forEach(nodeId => addNodeStats(nodeId));

    if (previewId && !selectedNodes.has(previewId)) {
        addNodeStats(previewId);
    }

    let statsHtml = "";
    for (const [statName, value] of Object.entries(statMap)) {
        if (statName.endsWith("_type")) continue;
        const statType = statMap[statName + "_type"] || "count";
        if (statType === "percent") {
            statsHtml += `<p>${statName}: ${value}%</p>`;
        } else if (statType === "flat") {
            statsHtml += `<p>${statName}: +${value}</p>`;
        } else {
            statsHtml += `<p>${statName} (x${value})</p>`;
        }
    }
    if (statsDisplay) {
        statsDisplay.innerHTML = statsHtml || "No stats to display.";
    }
}


function saveSelectedNodes() {
    const selected = Array.from(selectedNodes).map(id => {
        const node = nodes[id];
        const choice = selectedNodeChoices.get(id) || null; // Get choice or null
        const description = node?.stats || "No description available"; // Get stats or default
        return node
            ? { id: id, label: node.name, choice: choice, description: description }
            : { id: id, label: "Unknown Node", choice: null, description: "No description available" }; // Add default description here too
    });
    fetch('http://localhost:5001/save_nodes', { // Ensure port matches server.py
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ selectedNodes: selected }) // Send object with key
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Nodes saved:', data.message);
        if (saveButton) {
            saveButton.textContent = 'Saved!';
            setTimeout(() => {
                saveButton.textContent = 'Save Selected Nodes';
            }, 1500);
        }
    })
    .catch(error => {
        console.error('Error saving nodes:', error);
         if (saveButton) {
            saveButton.textContent = 'Save Failed!';
            setTimeout(() => {
                saveButton.textContent = 'Save Selected Nodes';
            }, 1500);
        }
    });
}

// --- Node Search Function ---
function findAndCenterNode(searchTerm) {
    // Clear previous search highlight and preview state
    searchedNodeId = null;
    previewNodeId = null;
    previewPath.clear();

    if (!searchTerm || searchTerm.trim() === '') {
        console.log("Search term is empty.");
        calculateStats();
        draw();
        return;
    }

    const lowerSearchTerm = searchTerm.toLowerCase();
    let foundNode = null;

    for (const [id, node] of Object.entries(nodes)) {
        if (node.name && node.name.toLowerCase().includes(lowerSearchTerm)) {
            foundNode = { ...node, id: id };
            break;
        }
    }

    if (foundNode && typeof foundNode.x === 'number' && typeof foundNode.y === 'number') {
        console.log(`Node found: ${foundNode.name} (ID: ${foundNode.id}) at (${foundNode.x}, ${foundNode.y})`);
        centerX = foundNode.x;
        centerY = foundNode.y;
        zoomLevel = 2;
        searchedNodeId = foundNode.id;
        calculateStats();
        draw();
    } else {
        alert(`Node containing "${searchTerm}" not found.`);
        calculateStats();
        draw();
    }
}


// --- Drawing Functions ---
function draw() {
    if (!ctx || !canvas) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.save();
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.scale(zoomLevel, zoomLevel);
    ctx.translate(-centerX, -centerY);

    const viewLeft = centerX - (canvas.width / 2) / zoomLevel;
    const viewRight = centerX + (canvas.width / 2) / zoomLevel;
    const viewTop = centerY - (canvas.height / 2) / zoomLevel;
    const viewBottom = centerY + (canvas.height / 2) / zoomLevel;

    const drawnConnections = new Set();
    connections.forEach(conn => {
        const fromNode = nodes[conn.from];
        const toNode = nodes[conn.to];
        if (!fromNode || !toNode) return;

        if (
            (fromNode.x < viewLeft && toNode.x < viewLeft) ||
            (fromNode.x > viewRight && toNode.x > viewRight) ||
            (fromNode.y < viewTop && toNode.y < viewTop) ||
            (fromNode.y > viewBottom && toNode.y > viewBottom)
        ) return;

        const connectId = getConnectId(conn.from, conn.to);
        if (drawnConnections.has(connectId)) return;
        drawnConnections.add(connectId);

        ctx.beginPath();
        ctx.moveTo(fromNode.x, fromNode.y);
        ctx.lineTo(toNode.x, toNode.y);

        const inSelectedPath = selectedPath.has(conn.from) && selectedPath.has(conn.to);
        const inPreviewPath = previewPath.has(conn.from) && previewPath.has(conn.to);

        if (inSelectedPath) {
            ctx.strokeStyle = '#FF00FF'; // magenta
            ctx.lineWidth = 6 / zoomLevel;
        } else if (inPreviewPath) {
            ctx.strokeStyle = '#00FFFF'; // cyan for preview path
            ctx.lineWidth = 4 / zoomLevel;
        } else if (conn.is_highlighted) {
            ctx.strokeStyle = '#00FF00';
            ctx.lineWidth = 4 / zoomLevel;
        } else {
            ctx.strokeStyle = '#666666';
            ctx.lineWidth = 2 / zoomLevel;
        }
        ctx.stroke();
    });

    for (const [id, node] of Object.entries(nodes)) {
        if (
            node.x < viewLeft - nodeSizes.keystone ||
            node.x > viewRight + nodeSizes.keystone ||
            node.y < viewTop - nodeSizes.keystone ||
            node.y > viewBottom + nodeSizes.keystone
        ) continue;

        const type = node.is_keystone ? "keystone" : node.is_notable ? "notable" : "normal";
        const radius = nodeSizes[type] / 2;
        ctx.beginPath();
        ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI);

        if (id === searchedNodeId) {
            ctx.fillStyle = '#32CD32'; // lime green
        } else if (selectedNodes.has(id)) {
            ctx.fillStyle = '#FF00FF'; // magenta
        } else if (previewPath.has(id)) {
            ctx.fillStyle = '#00FFFF'; // cyan preview path
        } else if (path.includes(id)) {
            ctx.fillStyle = '#00FF00'; // green original path
        } else {
            ctx.fillStyle = type === "keystone" ? '#FF4500' : type === "notable" ? '#FFD700' : '#888888';
        }

        ctx.fill();
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 1 / zoomLevel;
        ctx.stroke();
    }
    ctx.restore();
}

// --- Stream Processing Helper ---
function processStreamChunk(chunk, currentState) {
    let { isThinking, accumulatedThoughts } = currentState;
    let currentThoughtsChunk = null; // Chunk to potentially append incrementally
    let finalThoughtsOutput = null; // Final thoughts content when </think> found
    let responseChunk = null;
    let thinkStartIdx = -1;
    let thinkEndIdx = -1;
    let stateChanged = false; // Flag to indicate UI updates might be needed

    // Handle potential start tag
    thinkStartIdx = chunk.indexOf('<think>');
    if (thinkStartIdx !== -1 && !isThinking) {
        // Process content *before* <think> as response
        const beforeThink = chunk.substring(0, thinkStartIdx);
        if (beforeThink.length > 0) {
             responseChunk = (responseChunk || '') + beforeThink;
        }
        // Update state
        isThinking = true;
        accumulatedThoughts = ''; // Reset thoughts on new <think>
        stateChanged = true;
        // Remove tag and preceding part for further processing
        chunk = chunk.substring(thinkStartIdx + '<think>'.length);
    }

    // Handle potential end tag
    thinkEndIdx = chunk.indexOf('</think>');
    if (thinkEndIdx !== -1 && isThinking) {
        // Append content *before* </think> to thoughts
        const beforeEndThink = chunk.substring(0, thinkEndIdx);
        accumulatedThoughts += beforeEndThink;
        currentThoughtsChunk = beforeEndThink; // Part of thoughts from this chunk

        // Finalize thoughts for display
        finalThoughtsOutput = accumulatedThoughts;

        // Process content *after* </think> as response
        const afterEndThink = chunk.substring(thinkEndIdx + '</think>'.length);
         if (afterEndThink.length > 0) {
            responseChunk = (responseChunk || '') + afterEndThink;
        }

        // Update state
        isThinking = false;
        stateChanged = true;
        chunk = ''; // Consumed the rest of the chunk relevant to thoughts processing
    }

    // Process remaining chunk based on state
    if (isThinking && chunk.length > 0) {
        accumulatedThoughts += chunk; // Append to ongoing thoughts
        currentThoughtsChunk = chunk; // Part of thoughts from this chunk
    } else if (!isThinking && chunk.length > 0) {
        // Append if it wasn't already handled as part of start/end tag logic
        responseChunk = (responseChunk || '') + chunk;
    }

    return {
        currentThoughtsChunk: currentThoughtsChunk, // For potential incremental display
        finalThoughtsOutput: finalThoughtsOutput, // Populated only when </think> is found
        responseChunk: responseChunk,
        newState: { isThinking, accumulatedThoughts },
        stateChanged: stateChanged
    };
}


// --- Interactive LLM Response Appending ---
function appendInteractiveResponseChunk(chunk, targetElement) {
    if (!chunk) return;

    const nodeRegex = /([\w\s'-]+)\s\(ID:\s(\d+)\)/g; // Regex to find "Node Name (ID: 12345)"
    let lastIndex = 0;
    let match;

    while ((match = nodeRegex.exec(chunk)) !== null) {
        const [fullMatch, nodeName, nodeId] = match;
        const index = match.index;

        // Append text before the match
        if (index > lastIndex) {
            targetElement.appendChild(document.createTextNode(chunk.substring(lastIndex, index)));
        }

        // Create and append the clickable span
        const span = document.createElement('span');
        span.textContent = fullMatch;
        span.classList.add('clickable-node'); // Add class for identification and styling
        span.dataset.nodeName = nodeName.trim(); // Store node name
        span.dataset.nodeId = nodeId;       // Store node ID (optional, but good to have)
        span.style.cursor = 'pointer';      // Make it look clickable
        span.style.textDecoration = 'underline'; // Make it look clickable
        span.style.color = 'lightblue'; // Distinguish clickable text
        targetElement.appendChild(span);

        lastIndex = nodeRegex.lastIndex;
    }

    // Append any remaining text after the last match
    if (lastIndex < chunk.length) {
        targetElement.appendChild(document.createTextNode(chunk.substring(lastIndex)));
    }

    // Auto-scroll
    targetElement.scrollTop = targetElement.scrollHeight;
}


// --- Event Listeners ---
function setupEventListeners() {
    console.log('setupEventListeners: Function started.');
    if (!canvas) return;

    canvas.addEventListener('wheel', (e) => {
        e.preventDefault();
        const delta = e.deltaY;
        const oldZoomLevel = zoomLevel;

        // Calculate zoom factor based on direction
        const scaleMultiplier = 1 - delta * ZOOM_FACTOR;

        // Clamp zoom level
        zoomLevel = Math.min(MAX_ZOOM, Math.max(MIN_ZOOM, zoomLevel * scaleMultiplier));

        // Calculate mouse position in world coordinates before zoom
        const mouseXBefore = (e.clientX - canvas.width / 2) / oldZoomLevel + centerX;
        const mouseYBefore = (e.clientY - canvas.height / 2) / oldZoomLevel + centerY;

        // Calculate mouse position in world coordinates after zoom
        const mouseXAfter = (e.clientX - canvas.width / 2) / zoomLevel + centerX;
        const mouseYAfter = (e.clientY - canvas.height / 2) / zoomLevel + centerY;

        // Adjust center to keep mouse position stable
        centerX += (mouseXBefore - mouseXAfter);
        centerY += (mouseYBefore - mouseYAfter);

        draw();
    });

    canvas.addEventListener('mousedown', (e) => {
        isDragging = true;
        lastX = e.clientX;
        lastY = e.clientY;
        canvas.style.cursor = 'grabbing'; // Change cursor
    });

    canvas.addEventListener('mousemove', (e) => {
        const currentX = e.clientX;
        const currentY = e.clientY;

        if (isDragging) {
            const dx = (currentX - lastX) / zoomLevel;
            const dy = (currentY - lastY) / zoomLevel;
            centerX -= dx;
            centerY -= dy;
            lastX = currentX;
            lastY = currentY;
            draw();
        }

        // Tooltip logic
        const mouseX = (currentX - canvas.width / 2) / zoomLevel + centerX;
        const mouseY = (currentY - canvas.height / 2) / zoomLevel + centerY;
        let hoveredNode = null;
        let minDistSq = Infinity;

        for (const [id, node] of Object.entries(nodes)) {
            const type = node.is_keystone ? "keystone" : node.is_notable ? "notable" : "normal";
            const radius = nodeSizes[type] / 2;
            const dx = mouseX - node.x;
            const dy = mouseY - node.y;
            const distSq = dx * dx + dy * dy;

            if (distSq <= radius * radius && distSq < minDistSq) {
                hoveredNode = node;
                minDistSq = distSq; // Prioritize smaller nodes if overlapping
            }
        }

        if (hoveredNode && tooltip) {
            tooltip.style.display = 'block';
            tooltip.style.left = `${e.clientX + 10}px`; // Use currentX/Y for screen position
            tooltip.style.top = `${e.clientY + 10}px`;
            tooltip.innerText = `${hoveredNode.name}\n${hoveredNode.stats}`;
            canvas.style.cursor = 'pointer'; // Indicate clickable node
        } else if (tooltip) {
            tooltip.style.display = 'none';
            canvas.style.cursor = isDragging ? 'grabbing' : 'grab'; // Reset cursor
        }
    });

    canvas.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            canvas.style.cursor = 'grab'; // Reset cursor
        }
    });

     canvas.addEventListener('mouseleave', () => { // Hide tooltip if mouse leaves canvas
        if (tooltip) tooltip.style.display = 'none';
        if (isDragging) { // Stop dragging if mouse leaves while dragging
            isDragging = false;
            canvas.style.cursor = 'grab';
        }
    });
canvas.addEventListener('click', (e) => {
    const wasDrag = Math.abs(e.clientX - lastX) > 2 || Math.abs(e.clientY - lastY) > 2;

    if (wasDrag && isDragging) {
        // Allow click even after drag for now
    }

    const mouseX = (e.clientX - canvas.width / 2) / zoomLevel + centerX;
    const mouseY = (e.clientY - canvas.height / 2) / zoomLevel + centerY;
    let clickedNodeId = null;
    let minDistSq = Infinity;

    for (const [id, node] of Object.entries(nodes)) {
        const type = node.is_keystone ? "keystone" : node.is_notable ? "notable" : "normal";
        const radius = nodeSizes[type] / 2;
        const dx = mouseX - node.x;
        const dy = mouseY - node.y;
        const distSq = dx * dx + dy * dy;

        if (distSq <= radius * radius && distSq < minDistSq) {
            clickedNodeId = id;
            minDistSq = distSq;
        }
    }

    if (!clickedNodeId) return;

    // If clicked node is the search-highlighted node, trigger preview instead of selection
    if (clickedNodeId === searchedNodeId) {
        previewNodeId = clickedNodeId;

        // Calculate shortest path from any selected node to preview node
        previewPath.clear();
        let shortest = null;
        for (const selId of selectedNodes) {
            const pathCandidate = shortestPath(selId, clickedNodeId);
            if (!shortest || (pathCandidate.length > 0 && pathCandidate.length < shortest.length)) {
                shortest = pathCandidate;
            }
        }
        if (shortest && shortest.length > 0) {
            shortest.forEach(nid => previewPath.add(nid));
        }

        calculateStats(previewNodeId);
        draw();
        return; // Do not modify build or clear search
    }

    // Otherwise, clear preview state and search highlight
    previewNodeId = null;
    previewPath.clear();
    searchedNodeId = null;

    const clickedNode = nodes[clickedNodeId];
    const isAttributeNode = clickedNode && clickedNode.stats && clickedNode.stats.trim() === "+5 to any Attribute";

    if (selectedNodes.has(clickedNodeId)) {
        selectedNodes.delete(clickedNodeId);
        if (isAttributeNode) {
            selectedNodeChoices.delete(clickedNodeId);
        }
        const index = clickedNodes.indexOf(clickedNodeId);
        if (index > -1) clickedNodes.splice(index, 1);
        updatePath();
        return;
    }

    if (selectedNodes.size === 0) {
        if (isAttributeNode) {
            let choice = null;
            while (choice === null) {
                const input = prompt(`Choose attribute for node "${clickedNode.name}":\nEnter 'str', 'dex', or 'int'`);
                if (input === null) return;
                const lowerInput = input.toLowerCase().trim();
                if (lowerInput === 'str') choice = 'Strength';
                else if (lowerInput === 'dex') choice = 'Dexterity';
                else if (lowerInput === 'int') choice = 'Intelligence';
                else alert("Invalid input. Please enter 'str', 'dex', or 'int'.");
                }
                selectedNodeChoices.set(clickedNodeId, choice);
            }
            selectedNodes.add(clickedNodeId);
            clickedNodes.push(clickedNodeId);
            updatePath();
            return;
        }

        // Check if clicked node is adjacent to any selected node
        let isAdjacent = false;
        for (const selId of selectedNodes) {
            if (connections.some(conn =>
                (conn.from === selId && conn.to === clickedNodeId) ||
                (conn.to === selId && conn.from === clickedNodeId))) {
                isAdjacent = true;
                break;
            }
        }

        if (isAdjacent) {
            // Directly add clicked node
            if (isAttributeNode) {
                let choice = null;
                while (choice === null) {
                    const input = prompt(`Choose attribute for node "${clickedNode.name}":\nEnter 'str', 'dex', or 'int'`);
                    if (input === null) return;
                    const lowerInput = input.toLowerCase().trim();
                    if (lowerInput === 'str') choice = 'Strength';
                    else if (lowerInput === 'dex') choice = 'Dexterity';
                    else if (lowerInput === 'int') choice = 'Intelligence';
                    else alert("Invalid input. Please enter 'str', 'dex', or 'int'.");
                }
                selectedNodeChoices.set(clickedNodeId, choice);
            }
            selectedNodes.add(clickedNodeId);
            clickedNodes.push(clickedNodeId);
            updatePath();
            return;
        }

        // Not adjacent: find nearest selected node
        let nearestId = null;
        let nearestDistSq = Infinity;
        for (const selId of selectedNodes) {
            const selNode = nodes[selId];
            const dx = selNode.x - clickedNode.x;
            const dy = selNode.y - clickedNode.y;
            const distSq = dx * dx + dy * dy;
            if (distSq < nearestDistSq) {
                nearestDistSq = distSq;
                nearestId = selId;
            }
        }

        if (!nearestId) return; // Fallback safety

        const pathNodeIds = shortestPath(nearestId, clickedNodeId);

        if (!pathNodeIds || pathNodeIds.length === 0) return;

        for (const nodeId of pathNodeIds) {
            const node = nodes[nodeId];
            const isAttr = node && node.stats && node.stats.trim() === "+5 to any Attribute";
            if (!selectedNodes.has(nodeId)) {
                if (isAttr) {
                    let choice = null;
                    while (choice === null) {
                        const input = prompt(`Choose attribute for node "${node.name}":\nEnter 'str', 'dex', or 'int'`);
                        if (input === null) return;
                        const lowerInput = input.toLowerCase().trim();
                        if (lowerInput === 'str') choice = 'Strength';
                        else if (lowerInput === 'dex') choice = 'Dexterity';
                        else if (lowerInput === 'int') choice = 'Intelligence';
                        else alert("Invalid input. Please enter 'str', 'dex', or 'int'.");
                    }
                    selectedNodeChoices.set(nodeId, choice);
                }
                selectedNodes.add(nodeId);
                clickedNodes.push(nodeId);
            }
        }

        updatePath();
    });
    // LLM Chat Interface Logic
    if (getSuggestionButton) {
        getSuggestionButton.addEventListener('click', () => {
            const user_query = userQueryInput ? userQueryInput.value : '';
            const build_goal = buildGoalInput ? buildGoalInput.value : 'General advice';
            if (!user_query) return;

            // --- Reset UI and State ---
            if (llmResponseOutput) llmResponseOutput.textContent = '';
            if (llmThoughtsOutput) llmThoughtsOutput.textContent = '';
            if (llmThoughtsContainer) llmThoughtsContainer.style.display = 'none'; // Hide initially
            if (toggleThoughtsButton) {
                 toggleThoughtsButton.innerHTML = '&gt; Thoughts...'; // Reset button text
                 toggleThoughtsButton.disabled = true; // Disable until thoughts arrive
            }
            if (llmThoughtsOutput) llmThoughtsOutput.style.display = 'none'; // Ensure content area is hidden

            let streamState = { isThinking: false, accumulatedThoughts: '' };
            let fullResponse = ''; // Variable to accumulate the full response
            let streamFinishedCleanly = false; // Flag to track clean stream end
            let eventSource = null;
            // --- End Reset ---

            if (llmResponseOutput) llmResponseOutput.textContent = 'Connecting to suggestion stream...'; // Keep this temporary message

            try {
                const params = new URLSearchParams({
                    user_query: user_query,
                    build_goal: build_goal
                });
                const historyParam = chatHistory.length > 0 ? `&history=${encodeURIComponent(JSON.stringify(chatHistory))}` : '';
                const url = `http://localhost:5001/get_suggestion?${params.toString()}${historyParam}`;

                eventSource = new EventSource(url);

                eventSource.onopen = function() {
                    console.log("SSE connection opened.");
                    if (llmResponseOutput) llmResponseOutput.textContent = ''; // Clear connecting message
                };

                eventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);

                        if (data.error) {
                            console.error("SSE error message:", data.error);
                            if (llmResponseOutput) llmResponseOutput.textContent += `\nServer Error: ${data.error}`;
                            streamState.isThinking = false; // Reset state on error
                            if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Error)';
                            eventSource.close();
                            return;
                        }

                        if (data.message && data.message === "Stream finished") {
                             console.log("SSE stream finished signal received.");
                             streamFinishedCleanly = true; // Mark clean finish
                             if (streamState.isThinking) {
                                 // Stream finished unexpectedly while still "thinking"
                                 console.warn("Stream finished while isThinking was true.");
                                 streamState.isThinking = false;
                                 if (llmThoughtsOutput) llmThoughtsOutput.textContent = streamState.accumulatedThoughts; // Show what we got
                                 if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = 'v Thoughts (Incomplete)';
                             }
                             eventSource.close();
                             // Add assistant response to history after stream closes cleanly
                             if (fullResponse) {
                                 const assistantMessage = { role: "assistant", content: fullResponse };
                                 chatHistory.push(assistantMessage);
                                 console.log("Assistant response added to history (Suggestion):", assistantMessage);
                             }
                             return;
                        }

                        if (data.content) {
                            const wasThinking = streamState.isThinking;
                            const result = processStreamChunk(data.content, streamState);
                            streamState = result.newState; // Update state

                            // Handle UI changes based on state transitions
                            if (result.stateChanged) {
                                if (streamState.isThinking && !wasThinking) {
                                    // Started thinking
                                    if (llmThoughtsContainer) llmThoughtsContainer.style.display = 'block';
                                    if (toggleThoughtsButton) {
                                        toggleThoughtsButton.innerHTML = 'v Thinking...';
                                        toggleThoughtsButton.disabled = false; // Enable button
                                    }
                                    if (llmThoughtsOutput) llmThoughtsOutput.textContent = ''; // Clear previous thoughts
                                } else if (!streamState.isThinking && wasThinking) {
                                    // Finished thinking
                                    if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = 'v Thoughts (Done)';
                                    // Final thoughts content is handled below via finalThoughtsOutput
                                }
                            }

                            // Append chunks to respective outputs
                            if (result.currentThoughtsChunk && streamState.isThinking && llmThoughtsOutput) {
                                // Optional: Append thoughts incrementally while thinking
                                llmThoughtsOutput.textContent += result.currentThoughtsChunk; // <-- Uncommented
                                llmThoughtsOutput.scrollTop = llmThoughtsOutput.scrollHeight; // Auto-scroll thoughts
                            }
                            if (result.finalThoughtsOutput && llmThoughtsOutput) {
                                // Display final thoughts when </think> is processed
                                llmThoughtsOutput.textContent = result.finalThoughtsOutput;
                                llmThoughtsOutput.scrollTop = llmThoughtsOutput.scrollHeight; // Auto-scroll thoughts
                            }
                            if (result.responseChunk && llmResponseOutput) {
                                // llmResponseOutput.textContent += result.responseChunk; // Old way
                                appendInteractiveResponseChunk(result.responseChunk, llmResponseOutput); // New way
                                fullResponse += result.responseChunk; // Accumulate response chunk
                            }
                                // llmResponseOutput.scrollTop = llmResponseOutput.scrollHeight; // Auto-scroll response (handled in helper)
                        }

                    } catch (e) {
                        console.error("Error parsing SSE data:", e, "Raw data:", event.data);
                        if (llmResponseOutput) llmResponseOutput.textContent += `\nError parsing stream data.`;
                        streamState.isThinking = false; // Reset state on parse error
                        if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Parse Error)';
                        eventSource.close();
                    }
                };

                eventSource.onerror = function(error) {
                    console.error("EventSource failed:", error);
                    if (llmResponseOutput) llmResponseOutput.textContent += `\nStream connection error.`;
                    streamState.isThinking = false; // Reset state on connection error
                    if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Connection Error)';
                    if (eventSource) {
                         eventSource.close();
                    }
                };

            } catch (error) {
                 console.error('Error setting up EventSource:', error);
                 if (llmResponseOutput) llmResponseOutput.textContent = `Error initiating connection: ${error.message}`;
                 streamState.isThinking = false; // Reset state on setup error
                 if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Setup Error)';
                 if (eventSource) {
                     eventSource.close();
                 }
            }
        });
    }


    // General LLM Chat Interface Logic (No Nodes)
    const askGeneralQuestionButton = document.getElementById('askGeneralQuestionButton'); // Get the new button
    if (askGeneralQuestionButton) {
        askGeneralQuestionButton.addEventListener('click', () => {
            const user_query = userQueryInput ? userQueryInput.value : '';
            const build_goal = buildGoalInput ? buildGoalInput.value : 'General advice';
            if (!user_query) return;
            const selected_stats_str = statsDisplay ? statsDisplay.innerHTML : ''; // Get stats from display

            // --- Reset UI and State ---
            if (llmResponseOutput) llmResponseOutput.textContent = '';
            if (llmThoughtsOutput) llmThoughtsOutput.textContent = '';
            if (llmThoughtsContainer) llmThoughtsContainer.style.display = 'none'; // Hide initially
             if (toggleThoughtsButton) {
                 toggleThoughtsButton.innerHTML = '&gt; Thoughts...'; // Reset button text
                 toggleThoughtsButton.disabled = true; // Disable until thoughts arrive
            }
            if (llmThoughtsOutput) llmThoughtsOutput.style.display = 'none'; // Ensure content area is hidden

            let streamState = { isThinking: false, accumulatedThoughts: '' };
            let eventSource = null;
            let fullResponseForHistory = ""; // Separate accumulator for history
            // --- End Reset ---

            // Add user query to history (do this *before* potential failure in setup)
            chatHistory.push({ role: 'user', content: user_query });

            if (llmResponseOutput) llmResponseOutput.textContent = 'Connecting to general chat stream...';

            try {
                const params = new URLSearchParams({
                    user_query: user_query,
                    build_goal: build_goal,
                    history: JSON.stringify(chatHistory) // Send history
                });
                params.append('selected_stats', selected_stats_str); // Send calculated stats
                const url = `http://localhost:5001/ask_general?${params.toString()}`;

                eventSource = new EventSource(url);

                eventSource.onopen = function() {
                    console.log("General SSE connection opened.");
                    if (llmResponseOutput) llmResponseOutput.textContent = ''; // Clear connecting message
                };

                eventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);

                        if (data.error) {
                            console.error("General SSE error message:", data.error);
                            if (llmResponseOutput) llmResponseOutput.textContent += `\nServer Error: ${data.error}`;
                            streamState.isThinking = false; // Reset state on error
                            if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Error)';
                            eventSource.close();
                            // Don't add assistant response to history on server error
                            return;
                        }

                        if (data.message && data.message === "Stream finished") {
                             console.log("General SSE stream finished signal received.");
                             if (streamState.isThinking) {
                                 // Stream finished unexpectedly while still "thinking"
                                 console.warn("Stream finished while isThinking was true.");
                                 streamState.isThinking = false;
                                 if (llmThoughtsOutput) llmThoughtsOutput.textContent = streamState.accumulatedThoughts; // Show what we got
                                 if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = 'v Thoughts (Incomplete)';
                             }
                             // Add assistant response to history AFTER stream finishes successfully
                             chatHistory.push({ role: 'assistant', content: fullResponseForHistory });
                             console.log("Updated Chat History:", chatHistory); // For debugging
                             eventSource.close();
                             return;
                        }

                        if (data.content) {
                            const wasThinking = streamState.isThinking;
                            const result = processStreamChunk(data.content, streamState);
                            streamState = result.newState; // Update state

                            // Handle UI changes based on state transitions
                            if (result.stateChanged) {
                                if (streamState.isThinking && !wasThinking) {
                                    // Started thinking
                                    if (llmThoughtsContainer) llmThoughtsContainer.style.display = 'block';
                                    if (toggleThoughtsButton) {
                                        toggleThoughtsButton.innerHTML = 'v Thinking...';
                                        toggleThoughtsButton.disabled = false; // Enable button
                                    }
                                    if (llmThoughtsOutput) llmThoughtsOutput.textContent = ''; // Clear previous thoughts
                                } else if (!streamState.isThinking && wasThinking) {
                                    // Finished thinking
                                    if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = 'v Thoughts (Done)';
                                    // Final thoughts content is handled below
                                }
                            }

                            // Append chunks to respective outputs
                            if (result.currentThoughtsChunk && streamState.isThinking && llmThoughtsOutput) {
                                // Optional: Append thoughts incrementally
                                llmThoughtsOutput.textContent += result.currentThoughtsChunk; // <-- Uncommented
                                llmThoughtsOutput.scrollTop = llmThoughtsOutput.scrollHeight; // Auto-scroll thoughts
                            }
                             if (result.finalThoughtsOutput && llmThoughtsOutput) {
                                // Display final thoughts when </think> is processed
                                llmThoughtsOutput.textContent = result.finalThoughtsOutput;
                                llmThoughtsOutput.scrollTop = llmThoughtsOutput.scrollHeight; // Auto-scroll thoughts
                            }
                            if (result.responseChunk && llmResponseOutput) {
                                llmResponseOutput.textContent += result.responseChunk;
                                fullResponseForHistory += result.responseChunk; // Accumulate only response part for history
                            }
                                llmResponseOutput.scrollTop = llmResponseOutput.scrollHeight; // Auto-scroll response
                        }

                    } catch (e) {
                        console.error("Error parsing general SSE data:", e, "Raw data:", event.data);
                        if (llmResponseOutput) llmResponseOutput.textContent += `\nError parsing stream data.`;
                        streamState.isThinking = false; // Reset state on parse error
                        if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Parse Error)';
                        // Don't add incomplete/error response to history
                        eventSource.close(); // Close on parse error too
                    }
                };

                eventSource.onerror = function(error) {
                    console.error("General EventSource failed:", error);
                    if (llmResponseOutput) llmResponseOutput.textContent += `\nStream connection error.`;
                    streamState.isThinking = false; // Reset state on connection error
                    if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Connection Error)';
                    if (eventSource) {
                         eventSource.close();
                    }
                    // Don't add anything to history on connection error
                };

            } catch (error) {
                 console.error('Error setting up general EventSource:', error);
                 if (llmResponseOutput) llmResponseOutput.textContent = `Error initiating connection: ${error.message}`;
                 streamState.isThinking = false; // Reset state on setup error
                 if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Setup Error)';
                 if (eventSource) {
                     eventSource.close();
                 }
                 // Don't add user message to history if setup fails
                 chatHistory.pop(); // Remove the user message we added optimistically
            }
        });
    }

     // Save Button Listener
    if (saveButton) {
        saveButton.addEventListener('click', saveSelectedNodes);
    }

    // Thoughts Toggle Listener
    if (toggleThoughtsButton && llmThoughtsOutput && llmThoughtsContainer) {
        // Container is hidden by default (CSS or reset logic). Button is disabled initially.
        // llmThoughtsContainer.style.display = 'none'; // Ensure hidden initially if not done by CSS
        // toggleThoughtsButton.disabled = true; // Ensure disabled initially

        toggleThoughtsButton.addEventListener('click', () => {
            // Only toggle if there are thoughts to show (button is enabled)
            if (!toggleThoughtsButton.disabled) {
                 const isHidden = llmThoughtsOutput.style.display === 'none';
                 llmThoughtsOutput.style.display = isHidden ? 'block' : 'none';
                 // Update button text based on visibility and current state (Thinking/Done etc.)
                 if (isHidden) {
                     // Update text based on whether it was 'Thinking' or 'Done'
                     if (toggleThoughtsButton.innerHTML.includes('Thinking')) {
                         toggleThoughtsButton.innerHTML = 'v Thinking...';
                     } else {
                         // Handle various final states (Done, Incomplete, Error)
                         if (toggleThoughtsButton.innerHTML.includes('Incomplete')) {
                            toggleThoughtsButton.innerHTML = 'v Thoughts (Incomplete)';
                         } else if (toggleThoughtsButton.innerHTML.includes('Error')) {
                            toggleThoughtsButton.innerHTML = 'v Thoughts (Error)'; // Keep error state visible
                         }
                         else {
                            toggleThoughtsButton.innerHTML = 'v Thoughts (Done)';
                         }
                     }
                 } else {
                     // Always show '>' when hiding the content
                     toggleThoughtsButton.innerHTML = '&gt; Thoughts...';
                 }
            }
        });
    }

    // Clear Chat History Button Logic
    const clearChatHistoryButton = document.getElementById('clearChatHistoryButton');
    if (clearChatHistoryButton) {
        clearChatHistoryButton.addEventListener('click', () => {
            chatHistory = []; // Clear the internal history array
            if (llmResponseOutput) llmResponseOutput.textContent = ''; // Clear response display
            if (llmThoughtsOutput) llmThoughtsOutput.textContent = ''; // Clear thoughts display
            if (llmThoughtsContainer) llmThoughtsContainer.style.display = 'none'; // Hide thoughts container
            console.log("Chat history cleared.");

            // Also clear backend persistent context
            fetch('http://localhost:5001/clear_context?build_id=default_build', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                console.log('Backend context cleared:', data);
            })
            .catch(error => {
                console.error('Error clearing backend context:', error);
            });
        });
    }

   // Node Search Event Listeners (Moved from incorrect nesting)
   if (nodeSearchInput) {
       nodeSearchInput.addEventListener('keypress', (e) => {
           if (e.key === 'Enter') {
               findAndCenterNode(nodeSearchInput.value);
           }
       });
   }
   if (nodeSearchButton) {
       nodeSearchButton.addEventListener('click', () => {
           console.log('Search button clicked!'); // Keep this log for confirmation
           findAndCenterNode(nodeSearchInput.value);
       });
       console.log('Search button listener attached.'); // Keep this log for confirmation
   }

   // Image Upload Logic (Moved inside setupEventListeners)
   if (imageUploadInput) {
       imageUploadInput.addEventListener('change', async (event) => {
           const files = event.target.files;
           if (!files || files.length === 0) {
               if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'No images selected';
               uploadedImageFilenames = [];
               return;
           }

           uploadedImageFilenames = [];
           if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'Uploading...';

           // Prepare FormData with all files
           const formData = new FormData();
           for (const file of files) {
               formData.append('image_file', file);
           }

           try {
               const response = await fetch('/upload_image', {
                   method: 'POST',
                   body: formData
               });

               if (!response.ok) {
                   let errMsg = `HTTP error! status: ${response.status}`;
                   try {
                       const err = await response.json();
                       errMsg = err.error || errMsg;
                   } catch {}
                   throw new Error(errMsg);
               }

               const data = await response.json();
               if (Array.isArray(data.filenames) && data.filenames.length > 0) {
                   uploadedImageFilenames.push(...data.filenames);
                   if (uploadedFileNameSpan) {
                       uploadedFileNameSpan.textContent = `Uploaded ${uploadedImageFilenames.length} image(s): ${uploadedImageFilenames.join(', ')}`;
                   }
                   console.log('Images uploaded successfully:', data.filenames);
               } else {
                   if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'No images uploaded';
                   throw new Error('Upload successful, but no filenames received.');
               }
           } catch (error) {
               console.error('Error uploading images:', error);
               if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'Upload failed';
               uploadedImageFilenames = [];
               imageUploadInput.value = ''; // Clear the file input
               alert(`Image upload failed: ${error.message}`);
           }
       });
       console.log('Image upload listener attached.');
   }

   // Get Suggestion with Image Logic (Moved inside setupEventListeners)
   if (getSuggestionWithImageButton) {
       getSuggestionWithImageButton.addEventListener('click', () => {
           console.log('Get Suggestion with Image button clicked!');
           if (!uploadedImageFilenames || uploadedImageFilenames.length === 0) {
               alert('Please upload at least one image first.');
               return;
           }

           const user_query = userQueryInput ? userQueryInput.value : '';
           const build_goal = buildGoalInput ? buildGoalInput.value : 'General advice';
           if (!user_query) {
               alert('Please enter a query.');
               return;
           }

           if (llmResponseOutput) llmResponseOutput.textContent = '';
           if (llmThoughtsOutput) llmThoughtsOutput.textContent = '';
           if (llmThoughtsContainer) llmThoughtsContainer.style.display = 'none';
           if (toggleThoughtsButton) {
               toggleThoughtsButton.innerHTML = '&gt; Thoughts...';
               toggleThoughtsButton.disabled = true;
           }
           if (llmThoughtsOutput) llmThoughtsOutput.style.display = 'none';

           let streamState = { isThinking: false, accumulatedThoughts: '' };
           let eventSource = null;

           if (llmResponseOutput) llmResponseOutput.textContent = 'Connecting to image suggestion stream...';

           const selectedNodesArray = Array.from(selectedNodes);
           const selectedNodesJson = JSON.stringify(selectedNodesArray);
           const selectedChoicesObject = Object.fromEntries(selectedNodeChoices);
           const selectedChoicesJson = JSON.stringify(selectedChoicesObject);

           const requestBody = {
               user_query: user_query,
               build_goal: build_goal,
               image_filenames: uploadedImageFilenames,
               selected_nodes: selectedNodesJson,
               selected_choices: selectedChoicesJson,
               history: chatHistory
           };

           let fullResponse = '';
           fetch('/get_suggestion_with_image', {
               method: 'POST',
               headers: {
                   'Content-Type': 'application/json',
               },
               body: JSON.stringify(requestBody)
           })
           .then(response => {
               if (!response.ok) {
                   return response.json().then(err => {
                       throw new Error(err.error || `HTTP error! status: ${response.status}`);
                   }).catch(() => {
                       throw new Error(`HTTP error! status: ${response.status}`);
                   });
               }
               if (llmResponseOutput) llmResponseOutput.textContent = '';

               if (!response.body) {
                   throw new Error('Response body is missing or not readable.');
               }

               const reader = response.body.getReader();
               const decoder = new TextDecoder();
               let buffer = '';

               function processStream() {
                   reader.read().then(({ value, done }) => {
                       if (done) {
                           console.log("Image Suggestion stream finished.");
                           if (fullResponse) {
                               const assistantMessage = { role: "assistant", content: fullResponse };
                               chatHistory.push(assistantMessage);
                               console.log("Assistant response added to history (Image):", assistantMessage);
                           }

                           if (streamState.isThinking) {
                               console.warn("Stream finished while isThinking was true.");
                               streamState.isThinking = false;
                               if (llmThoughtsOutput) llmThoughtsOutput.textContent = streamState.accumulatedThoughts;
                               if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = 'v Thoughts (Incomplete)';
                           }
                           uploadedImageFilenames = [];
                           if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'No images selected';
                           if (imageUploadInput) imageUploadInput.value = '';
                           return;
                       }

                       buffer += decoder.decode(value, { stream: true });

                       let lines = buffer.split('\n\n');
                       buffer = lines.pop();

                       lines.forEach(line => {
                           if (line.startsWith('data: ')) {
                               const jsonData = line.substring(6);
                               try {
                                   const data = JSON.parse(jsonData);

                                   if (data.error) {
                                       console.error("Image Suggestion SSE error:", data.error);
                                       if (llmResponseOutput) llmResponseOutput.textContent += `\nServer Error: ${data.error}`;
                                       streamState.isThinking = false;
                                       if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Error)';
                                       uploadedImageFilenames = [];
                                       if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'No images selected';
                                       if (imageUploadInput) imageUploadInput.value = '';
                                       reader.cancel();
                                       return;
                                   }

                                   if (data.content) {
                                       const wasThinking = streamState.isThinking;
                                       const result = processStreamChunk(data.content, streamState);
                                       streamState = result.newState;

                                       if (result.stateChanged) {
                                           if (streamState.isThinking && !wasThinking) {
                                               if (llmThoughtsContainer) llmThoughtsContainer.style.display = 'block';
                                               if (toggleThoughtsButton) {
                                                   toggleThoughtsButton.innerHTML = 'v Thinking...';
                                                   toggleThoughtsButton.disabled = false;
                                               }
                                               if (llmThoughtsOutput) llmThoughtsOutput.textContent = '';
                                           } else if (!streamState.isThinking && wasThinking) {
                                               if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = 'v Thoughts (Done)';
                                           }
                                       }

                                       if (result.currentThoughtsChunk && streamState.isThinking && llmThoughtsOutput) {
                                           llmThoughtsOutput.textContent += result.currentThoughtsChunk;
                                           llmThoughtsOutput.scrollTop = llmThoughtsOutput.scrollHeight;
                                       }
                                       if (result.finalThoughtsOutput && llmThoughtsOutput) {
                                           llmThoughtsOutput.textContent = result.finalThoughtsOutput;
                                           llmThoughtsOutput.scrollTop = llmThoughtsOutput.scrollHeight;
                                       }
                                       if (result.responseChunk && llmResponseOutput) {
                                           appendInteractiveResponseChunk(result.responseChunk, llmResponseOutput);
                                           fullResponse += result.responseChunk;
                                       }
                                   }
                               } catch (e) {
                                   console.error("Error parsing image suggestion stream data:", e, "Raw data:", jsonData);
                                   if (llmResponseOutput) llmResponseOutput.textContent += `\nError parsing stream data.`;
                                   streamState.isThinking = false;
                                   if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Parse Error)';
                                   uploadedImageFilenames = [];
                                   if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'No images selected';
                                   if (imageUploadInput) imageUploadInput.value = '';
                                   reader.cancel();
                                   return;
                               }
                           }
                       });

                       processStream();

                   }).catch(error => {
                       console.error("Error reading image suggestion stream:", error);
                       if (llmResponseOutput) llmResponseOutput.textContent += `\nStream reading error: ${error.message}`;
                       streamState.isThinking = false;
                       if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Stream Read Error)';
                       uploadedImageFilenames = [];
                       if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'No images selected';
                       if (imageUploadInput) imageUploadInput.value = '';
                   });
               }

               processStream();

           })
           .catch(error => {
               console.error('Error during fetch/stream setup for Image Suggestion:', error);
               if (llmResponseOutput) llmResponseOutput.textContent = `Error initiating connection: ${error.message}`;
               streamState.isThinking = false;
               if (toggleThoughtsButton) toggleThoughtsButton.innerHTML = '&gt; Thoughts (Setup Error)';
               uploadedImageFilenames = [];
               if (uploadedFileNameSpan) uploadedFileNameSpan.textContent = 'No images selected';
               if (imageUploadInput) imageUploadInput.value = '';
           });
       });
       console.log('Get Suggestion w/ Image button listener attached.');
   }

} // End of setupEventListeners function

    // --- Delegated Click Listener for LLM Response Nodes ---
    function setupLlmResponseClickListener() {
        if (llmResponseOutput) {
            llmResponseOutput.addEventListener('click', (event) => {
                const target = event.target;
                // Check if the clicked element is one of our clickable spans
                if (target.tagName === 'SPAN' && target.classList.contains('clickable-node')) {
                    const nodeName = target.dataset.nodeName;
                    if (nodeName) {
                        console.log(`Clicked node link: ${nodeName}`);
                        findAndCenterNode(nodeName);
                    } else {
                        console.warn("Clicked node span missing data-node-name attribute:", target);
                    }
                }
            });
        } else {
            console.warn("llmResponseOutput element not found, cannot add click listener.");
        }
    }

    // --- Image Upload Logic --- (Moved inside setupEventListeners)

    // --- Get Suggestion with Image Logic --- (Moved inside setupEventListeners)

    // --- Clear Chat History Logic --- (Duplicate removed, already exists inside setupEventListeners)


// --- Initialization Function ---
function initSkillTree(config) {
    // Assign data passed from HTML
    nodes = config.nodes || {};
    connections = config.connections || [];
    path = config.path || [];
    constants = config.constants || {};
    initial_center_x = config.initial_center_x || 0;
    initial_center_y = config.initial_center_y || 0;
    tree_width = config.tree_width || 0;
    tree_height = config.tree_height || 0;

    // Get DOM elements
    canvas = document.getElementById('skillTree');
    tooltip = document.getElementById('tooltip');
    statsDisplay = document.getElementById('statsDisplay');
    nodeSearchInput = document.getElementById('nodeSearchInput');
    nodeSearchButton = document.getElementById('nodeSearchButton');
    userQueryInput = document.getElementById('userQueryInput');
    buildGoalInput = document.getElementById('buildGoalInput');
    getSuggestionButton = document.getElementById('getSuggestionButton');
    llmResponseOutput = document.getElementById('llmResponseOutput');
    saveButton = document.getElementById('saveButton');
    llmThoughtsContainer = document.getElementById('llmThoughtsContainer');
    toggleThoughtsButton = document.getElementById('toggleThoughtsButton');
    llmThoughtsOutput = document.getElementById('llmThoughtsOutput');
    imageUploadInput = document.getElementById('imageUploadInput');
    uploadedFileNameSpan = document.getElementById('uploadedFileName');
    getSuggestionWithImageButton = document.getElementById('getSuggestionWithImageButton');


    if (!canvas) {
    setupLlmResponseClickListener(); // Ensure listener is set up after llmResponseOutput is assigned
        console.error("Canvas element #skillTree not found!");
        return;
    }
    ctx = canvas.getContext('2d');

    // Set initial canvas size and view
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    centerX = initial_center_x;
    centerY = initial_center_y;

    // Calculate initial zoom to fit tree (optional, adjust padding)
    const padding = 200;
    if (tree_width > 0 && tree_height > 0) {
        const zoomX = canvas.width / (tree_width + padding);
        const zoomY = canvas.height / (tree_height + padding);
        zoomLevel = Math.min(zoomX, zoomY, 1); // Ensure zoom doesn't exceed 1 initially
    } else {
        zoomLevel = 1; // Default zoom if tree dimensions are zero
    }


    // Setup listeners and draw initial state
    // --- Draggable Chat Interface Logic ---
    const chatInterface = document.getElementById('chatInterface');
    const chatHeader = chatInterface ? chatInterface.querySelector('h4') : null; // Use the h4 as the handle

    if (chatInterface && chatHeader) {
        let isDraggingChat = false;
        let offsetX, offsetY;

        // Ensure the chatInterface has position: absolute (set in CSS, but good practice)
        chatInterface.style.position = 'absolute'; 

        chatHeader.addEventListener('mousedown', (e) => {
            // Only drag with left mouse button
            if (e.button !== 0) return;

            isDraggingChat = true;
            // Calculate offset from the top-left corner of the chatInterface
            offsetX = e.clientX - chatInterface.offsetLeft;
            offsetY = e.clientY - chatInterface.offsetTop;

            // Change cursor and prevent text selection
            chatHeader.style.cursor = 'grabbing';
            document.body.style.userSelect = 'none'; // Prevent text selection during drag
            document.body.style.webkitUserSelect = 'none'; // For Safari/Chrome
            document.body.style.msUserSelect = 'none'; // For IE

            // Add listeners to the document to track mouse movement everywhere
            document.addEventListener('mousemove', onMouseMoveChat);
            document.addEventListener('mouseup', onMouseUpChat);
        });

        function onMouseMoveChat(e) {
            if (!isDraggingChat) return;

            // Calculate new position
            const newLeft = e.clientX - offsetX;
            const newTop = e.clientY - offsetY;

            // Apply new position (ensure it stays within viewport bounds - optional enhancement)
            // Basic boundary check example:
            // const parentRect = document.body.getBoundingClientRect(); // Or specific container
            // const chatRect = chatInterface.getBoundingClientRect();
            // const constrainedLeft = Math.max(0, Math.min(newLeft, parentRect.width - chatRect.width));
            // const constrainedTop = Math.max(0, Math.min(newTop, parentRect.height - chatRect.height));
            // chatInterface.style.left = `${constrainedLeft}px`;
            // chatInterface.style.top = `${constrainedTop}px`;

            chatInterface.style.left = `${newLeft}px`;
            chatInterface.style.top = `${newTop}px`;
        }

        function onMouseUpChat(e) {
             // Only react to left mouse button up
            if (e.button !== 0) return;

            if (isDraggingChat) {
                isDraggingChat = false;
                chatHeader.style.cursor = 'grab'; // Restore cursor
                document.body.style.userSelect = ''; // Re-enable text selection
                document.body.style.webkitUserSelect = '';
                document.body.style.msUserSelect = '';

                // Remove global listeners
                document.removeEventListener('mousemove', onMouseMoveChat);
                document.removeEventListener('mouseup', onMouseUpChat);
            }
        }

        // Initial cursor style for the handle
        chatHeader.style.cursor = 'grab';
    } else {
        console.warn("Chat interface or header not found for dragging functionality.");
    }
    // --- End Draggable Chat Interface Logic ---


    setupEventListeners();
    calculateStats(); // Calculate initial stats if needed
    draw();
        // --- Load Saved Nodes ---
        console.log('Attempting to load saved nodes...');
        fetch('/load_nodes')
            .then(response => {
                if (!response.ok) {
                    // Handle cases like 404 Not Found if the file doesn't exist yet
                    if (response.status === 404) {
                        console.log('No saved nodes file found (/load_nodes). Starting fresh.');
                        return null; // Indicate no data to load
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(savedNodes => {
                console.log('Received saved node data:', savedNodes);
                // Check if savedNodes is null (from 404 handler) or not an array
                if (!savedNodes) {
                    return; // Nothing to load
                }

                if (Array.isArray(savedNodes)) {
                    console.log('Loading saved nodes:', savedNodes);
                    savedNodes.forEach(node => {
                        if (node && node.id !== undefined) { // Check if id exists
                            const nodeIdStr = node.id.toString(); // Ensure ID is string
                            selectedNodes.add(nodeIdStr);
                            if (node.choice) {
                                selectedNodeChoices.set(nodeIdStr, node.choice);
                            }
                        } else {
                            console.warn('Received invalid node data entry:', node);
                        }
                    });
                    console.log('Populated selectedNodes:', selectedNodes);
                    console.log('Populated selectedNodeChoices:', selectedNodeChoices);
                    console.log('Calling updatePath() to draw loaded nodes...');
                    updatePath(); // Update stats and redraw with loaded nodes
                    console.log('Node loading process complete.');
                } else {
                    console.warn('Received non-array data from /load_nodes:', savedNodes);
                }
            })
            .catch(error => {
                console.error('Error loading saved nodes:', error);
                console.error('Error loading saved nodes:', error);
                // Optionally inform the user or handle the error state,
                // but don't prevent the rest of the tree from initializing.
            });
        // --- End Load Saved Nodes ---


    // Handle window resize
    window.addEventListener('resize', () => {
        if (canvas) {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            draw(); // Redraw on resize
        }
    });
     // Initial setup for cursor
    if (canvas) {
        canvas.style.cursor = 'grab';
    }
    console.log('initSkillTree: Function finished.');
}

// The initSkillTree function will be called from the HTML script block
// Example call in HTML:
// <script>
//   const skillTreeData = { nodes: {...}, connections: [...], ... };
//   initSkillTree(skillTreeData);
// </script>


// --- Auto-Initialization --- 
// Check if skillTreeConfig is defined (by the HTML) and call init
if (typeof skillTreeConfig !== 'undefined') {
    initSkillTree(skillTreeConfig);
} else {
    console.error('skillTreeConfig is not defined. Ensure it is embedded correctly in the HTML before loading skill_tree.js');
}
