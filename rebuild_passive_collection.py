import json
import os
import logging
from sentence_transformers import SentenceTransformer
import chromadb
from chromadb.utils import embedding_functions
from modules.load_gems_passives_to_chroma import (
    _initialize_chroma_client,
    _get_embedding_model,
    load_passives_to_db,
    PASSIVES_COLLECTION_NAME
)

logging.basicConfig(level=logging.INFO)

PASSIVE_DB_PATH = "data/chroma_db"
PASSIVE_DATA_PATH = "data/passive_skills.json"
EMBEDDING_MODEL_NAME = "all-MiniLM-L6-v2"

def preprocess_passive_data(raw_data):
    processed = []
    for entry in raw_data:
        passive_id = str(entry[0])
        name = entry[1] or ""
        description = entry[2] or ""
        # Combine name and description for embedding
        doc = f"{name}. {description}".strip()
        metadata = {
            "name": name,
            "description": description,
            "class": entry[-1] if len(entry) > 10 else ""
        }
        processed.append({
            "id": passive_id,
            "document": doc,
            "metadata": metadata
        })
    return processed

def main():
    client = chromadb.PersistentClient(path=PASSIVE_DB_PATH)

    # Delete existing collection if exists
    try:
        client.delete_collection(PASSIVES_COLLECTION_NAME)
        logging.info(f"Deleted existing collection '{PASSIVES_COLLECTION_NAME}'.")
    except Exception as e:
        logging.warning(f"Could not delete collection (may not exist): {e}")

    # Recreate collection
    try:
        sentence_transformer_ef = embedding_functions.SentenceTransformerEmbeddingFunction(model_name=EMBEDDING_MODEL_NAME)
        collection = client.get_or_create_collection(
            name=PASSIVES_COLLECTION_NAME,
            metadata={"hnsw:space": "cosine", "embedding_function": "sentence_transformer"},
            embedding_function=sentence_transformer_ef
        )
        logging.info(f"Created collection '{PASSIVES_COLLECTION_NAME}' with sentence_transformer embedding.")
    except Exception as e:
        logging.error(f"Failed to create collection: {e}")
        return

    # Load embedding model
    embedding_model = SentenceTransformer(EMBEDDING_MODEL_NAME)

    # Load passive data
    with open(PASSIVE_DATA_PATH, "r", encoding="utf-8") as f:
        raw_data = json.load(f)

    processed_data = preprocess_passive_data(raw_data)

    # Load into DB
    load_passives_to_db(client, embedding_model, processed_data)

if __name__ == "__main__":
    main()