import os
import json

CONTEXT_DIR = "data/build_contexts"

def _get_context_path(build_id):
    os.makedirs(CONTEXT_DIR, exist_ok=True)
    return os.path.join(CONTEXT_DIR, f"{build_id}.json")

def load_context(build_id):
    """Load persistent context for a build/session."""
    path = _get_context_path(build_id)
    if os.path.exists(path):
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    else:
        return {
            "goals": [],
            "queries": [],
            "image_info": [],
            "key_stats": [],
            "skills": [],
            "passives": [],
            "item_summaries": [],
            "other_notes": [],
            "history": []  # List of {"role": "user"/"assistant", "content": "..."}
        }

def save_context(build_id, context):
    """Save persistent context for a build/session."""
    path = _get_context_path(build_id)
    with open(path, "w", encoding="utf-8") as f:
        json.dump(context, f, indent=2)

def update_context(build_id, new_info):
    """
    Update context with new info dict.
    Keys can include: goals, queries, image_info, key_stats, skills, passives, item_summaries, other_notes.
    """
    context = load_context(build_id)
    for key, value in new_info.items():
        if key not in context:
            context[key] = []
        if isinstance(value, list):
            context[key].extend(value)
        else:
            context[key].append(value)
    save_context(build_id, context)

def get_context_snippet(build_id):
    """
    Generate a text snippet summarizing the persistent context for LLM prompts or retrieval enrichment.
    """
    context = load_context(build_id)
    parts = []
    if context.get("goals"):
        parts.append("Build Goals: " + "; ".join(context["goals"]))
    if context.get("queries"):
        parts.append("Previous Queries: " + "; ".join(context["queries"]))
    if context.get("image_info"):
        parts.append("Image Insights: " + "; ".join(context["image_info"]))
    if context.get("key_stats"):
        parts.append("Key Stats: " + "; ".join(context["key_stats"]))
    if context.get("skills"):
        parts.append("Skills: " + "; ".join(context["skills"]))
    if context.get("passives"):
        parts.append("Passives: " + "; ".join(context["passives"]))
    if context.get("item_summaries"):
        parts.append("Items Seen: " + "; ".join(context["item_summaries"]))
    if context.get("other_notes"):
        parts.append("Notes: " + "; ".join(context["other_notes"]))
    return "\n".join(parts)