from dotenv import load_dotenv
load_dotenv()
import argparse
import asyncio
import json
import time
import logging
import os
import subprocess
import sys
import webbrowser
from datetime import datetime

# Existing imports
from modules.fetch_data import fetch_gem_data, fetch_passive_skill_data, SKILL_GEM_URL, SUPPORT_GEM_URL, PASSIVE_SKILL_TREE_URL
from modules.process_data import process_gems_for_chroma, process_passives_for_chroma, select_skill_gem, select_support_gems, identify_key_stats, score_node, find_path, FOCUS_KEYWORDS
from modules.visualize import generate_interactive_skill_tree
from modules.llm_integration import generate_llm_build_data
from modules.load_items_to_chroma import load_items_to_db, _initialize_chroma_client
from modules.load_gems_passives_to_chroma import load_gems_to_db, load_passives_to_db, _get_embedding_model as get_embedding_model, CHROMA_DB_BUILD_PATH, GEMS_COLLECTION_NAME as GEM_COLLECTION_NAME, PASSIVES_COLLECTION_NAME
from scrape_poe2_items import scrape_and_save_items


# --- Logging Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Constants ---
ITEM_RAW_DATA_FILE = "data/poe2_items_raw.json"
CHROMA_DB_PATH = "data/chroma_db/"
CHROMA_DB_BUILD_PATH = "data/chroma_db_build/"
GEM_COLLECTION_NAME = "poe2_gems"
PASSIVES_COLLECTION_NAME = "poe2_passives"
ITEM_COLLECTION_NAME = "poe2_items"
EMBEDDING_MODEL_NAME = 'all-MiniLM-L6-v2'
HTML_OUTPUT_FILE = "data/poe2_skill_tree_interactive.html"
NODES_DATA_FILE = "data/nodes_data.json"
CONNECTIONS_DATA_FILE = "data/connections_data.json"
BUILD_OUTPUT_FILE = "data/poe2_build.txt"
ALL_GEMS_FILE = "data/all_gems.json"
PASSIVE_SKILLS_FILE = "data/passive_skills.json"
CLASS_START_NODES_FILE = "data/class_start_nodes.json"
PASSIVE_RAW_DATA_FILE = "data/passive_raw_data.json"


async def fetch_data_and_build_db():
    """Fetches all required data and populates/rebuilds ChromaDB databases."""
    logger.info("--- Starting Data Fetch and DB Build ---")
    os.makedirs("data", exist_ok=True)

    # --- Fetch Data Asynchronously ---
    logger.info("Fetching skill gem data...")
    skill_gems = await fetch_gem_data(SKILL_GEM_URL, "Skill")
    logger.info(f"Found {len(skill_gems)} skill gems.")

    logger.info("Fetching support gem data...")
    support_gems = await fetch_gem_data(SUPPORT_GEM_URL, "Support")
    logger.info(f"Found {len(support_gems)} support gems.")

    all_gems = skill_gems + support_gems
    all_gems.sort(key=lambda x: (x["type"] != "Skill", x["name"]))
    logger.info(f"Total gems collected: {len(all_gems)}.")
    # Save all gems
    try:
        logger.info(f"Saving all gem data to {ALL_GEMS_FILE}...")
        with open(ALL_GEMS_FILE, "w", encoding="utf-8") as f:
            json.dump(all_gems, f, indent=2) # Use indent for readability
        logger.info("All gem data saved.")
    except Exception as e:
        logger.error(f"Error saving all gem data: {e}")


    logger.info("Fetching passive skill tree data...")
    passive_skills, nodes, connections, class_start_nodes, passive_data = fetch_passive_skill_data(PASSIVE_SKILL_TREE_URL)
    if not passive_data:
        logger.error("Failed to fetch passive skill tree data. Cannot proceed with DB build.")
        return # Or raise an error
    logger.info(f"Found {len(passive_skills)} passive skill nodes.")

    logger.info("Fetching item data...")
    await scrape_and_save_items()
    logger.info("Item data fetching complete.")

    # --- Save Nodes and Connections ---
    try:
        logger.info(f"Saving nodes data to {NODES_DATA_FILE}...")
        with open(NODES_DATA_FILE, "w", encoding="utf-8") as f:
            json.dump(nodes, f)
        logger.info(f"Saving connections data to {CONNECTIONS_DATA_FILE}...")
        # Ensure connections are JSON serializable (e.g., convert sets to lists if needed)
        serializable_connections = {k: list(v) for k, v in connections.items()} if connections else {}
        with open(CONNECTIONS_DATA_FILE, "w", encoding="utf-8") as f:
            json.dump(serializable_connections, f)
        logger.info("Nodes and connections data saved.")
    except Exception as e:
        logger.error(f"Error saving nodes/connections data: {e}")

    # Save other passive data components
    try:
        logger.info(f"Saving passive skills data to {PASSIVE_SKILLS_FILE}...")
        with open(PASSIVE_SKILLS_FILE, "w", encoding="utf-8") as f:
            json.dump(passive_skills, f)
        logger.info(f"Saving class start nodes data to {CLASS_START_NODES_FILE}...")
        with open(CLASS_START_NODES_FILE, "w", encoding="utf-8") as f:
            json.dump(class_start_nodes, f)
        logger.info(f"Saving raw passive tree data to {PASSIVE_RAW_DATA_FILE}...")
        with open(PASSIVE_RAW_DATA_FILE, "w", encoding="utf-8") as f:
            json.dump(passive_data, f) # Save the raw structure needed by visualize
        logger.info("Other passive data components saved.")
    except Exception as e:
        logger.error(f"Error saving other passive data components: {e}")

    # --- Process Data for ChromaDB ---
    logger.info("Processing gem data for ChromaDB...")
    processed_gems = process_gems_for_chroma(all_gems) if all_gems else []
    logger.info(f"Processed {len(processed_gems)} gems.")
    logger.info("Processing passive skill data for ChromaDB...")
    processed_passives = process_passives_for_chroma(passive_skills) if passive_skills else []
    logger.info(f"Processed {len(processed_passives)} passives.")

    # --- Initialize ChromaDB Clients and Embedding Model ---
    logger.info("Initializing ChromaDB client for items...")
    chroma_client = None
    try:
        chroma_client = _initialize_chroma_client(CHROMA_DB_PATH)
        logger.info("Item ChromaDB client initialized.")
    except Exception as e:
        logger.error(f"Error initializing Item ChromaDB client: {e}. Skipping item DB operations.")

    logger.info("Initializing embedding model...")
    embedding_model = None
    try:
        embedding_model = get_embedding_model(EMBEDDING_MODEL_NAME)
        logger.info("Embedding model initialized.")
    except Exception as e:
        logger.error(f"Fatal Error: Failed to initialize embedding model: {e}. Skipping all DB operations.")
        chroma_client = None # Prevent item DB ops

    chroma_client_build = None
    if embedding_model: # Only proceed if model loaded
        logger.info(f"Initializing ChromaDB client for build data at {CHROMA_DB_BUILD_PATH}...")
        try:
            chroma_client_build = _initialize_chroma_client(CHROMA_DB_BUILD_PATH)
            logger.info("Build ChromaDB client initialized.")
        except Exception as e:
            logger.error(f"Error initializing Build ChromaDB client: {e}. Skipping build DB operations.")
    else:
         logger.warning("Skipping Build ChromaDB initialization because embedding model failed.")

    # --- Load Items into ChromaDB ---
    if chroma_client and embedding_model: # Check both client and model
        logger.info(f"Loading items from {ITEM_RAW_DATA_FILE} into ChromaDB collection '{ITEM_COLLECTION_NAME}'...")
        try:
            if os.path.exists(ITEM_RAW_DATA_FILE):
                load_items_to_db(
                    raw_data_file=ITEM_RAW_DATA_FILE,
                    client=chroma_client,
                    collection_name=ITEM_COLLECTION_NAME,
                    embedding_model_name=EMBEDDING_MODEL_NAME # Pass name, function handles model loading
                )
                logger.info("Item loading into ChromaDB complete.")
            else:
                logger.warning(f"Item data file not found at {ITEM_RAW_DATA_FILE}. Skipping item loading.")
        except Exception as e:
            logger.error(f"Error loading items into ChromaDB: {e}")
    else:
         logger.warning("Skipping item loading into ChromaDB due to client or model initialization errors.")

    # --- Load Gems into Build ChromaDB ---
    if chroma_client_build and embedding_model and processed_gems:
        logger.info(f"Loading {len(processed_gems)} processed gems into Build ChromaDB collection '{GEM_COLLECTION_NAME}'...")
        try:
            load_gems_to_db(
                client=chroma_client_build,
                embedding_model=embedding_model, # Pass the loaded model object
                processed_gems_data=processed_gems,
                collection_name=GEM_COLLECTION_NAME
            )
            logger.info("Gem loading into Build ChromaDB complete.")
        except Exception as e:
            logger.error(f"Error loading gems into Build ChromaDB: {e}")
    elif not (chroma_client_build and embedding_model):
        logger.warning("Skipping gem loading into Build ChromaDB due to client or model initialization errors.")
    elif not processed_gems:
        logger.warning("Skipping gem loading into Build ChromaDB: No processed gem data available.")

    # --- Load Passives into Build ChromaDB ---
    if chroma_client_build and embedding_model and processed_passives:
        logger.info(f"Loading {len(processed_passives)} processed passive skills into Build ChromaDB collection '{PASSIVES_COLLECTION_NAME}'...")
        try:
            load_passives_to_db(
                client=chroma_client_build,
                embedding_model=embedding_model, # Pass the loaded model object
                processed_passives_data=processed_passives,
                collection_name=PASSIVES_COLLECTION_NAME
            )
            logger.info("Passive skill loading into Build ChromaDB complete.")
        except Exception as e:
            logger.error(f"Error loading passive skills into Build ChromaDB: {e}")
    elif not (chroma_client_build and embedding_model):
        logger.warning("Skipping passive skill loading into Build ChromaDB due to client or model initialization errors.")
    elif not processed_passives:
        logger.warning("Skipping passive skill loading into Build ChromaDB: No processed passive data available.")

    logger.info("--- Data Fetch and DB Build Finished ---")


async def generate_html_visualizer():
    """Generates the build path, LLM data, and interactive HTML visualization."""
    logger.info("--- Starting HTML Visualizer Generation ---")

    # --- Load Required Data from Local Files ---
    logger.info("Loading required data from local files for generation...")
    required_files = {
        "all_gems": ALL_GEMS_FILE,
        "passive_skills": PASSIVE_SKILLS_FILE,
        "nodes": NODES_DATA_FILE,
        "connections": CONNECTIONS_DATA_FILE,
        "class_start_nodes": CLASS_START_NODES_FILE,
        "passive_data": PASSIVE_RAW_DATA_FILE
    }
    loaded_data = {}
    missing_files = False

    for name, filepath in required_files.items():
        try:
            logger.info(f"Loading {name} data from {filepath}...")
            with open(filepath, 'r', encoding='utf-8') as f:
                loaded_data[name] = json.load(f)
            logger.info(f"Successfully loaded {name} data.")
        except FileNotFoundError:
            logger.error(f"Required data file not found: {filepath}. Cannot generate visualization.")
            missing_files = True
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {filepath}: {e}. Cannot generate visualization.")
            missing_files = True
        except Exception as e:
            logger.error(f"An unexpected error occurred loading {filepath}: {e}")
            missing_files = True

    if missing_files:
        logger.error("One or more required data files are missing or corrupted. Please run the script with the --fetch flag first.")
        return

    # Assign loaded data to variables
    all_gems = loaded_data["all_gems"]
    passive_skills = loaded_data["passive_skills"]
    nodes = loaded_data["nodes"]
    connections = loaded_data["connections"] # Keep as dict[str, list] as saved
    class_start_nodes = loaded_data["class_start_nodes"]
    passive_data = loaded_data["passive_data"] # Raw data for visualization

    # Split gems
    skill_gems = [gem for gem in all_gems if gem.get("type") == "Skill"]
    support_gems = [gem for gem in all_gems if gem.get("type") == "Support"]
    logger.info(f"Loaded {len(skill_gems)} skill gems and {len(support_gems)} support gems from file.")

    # --- Generate Build Path ---
    logger.info("Generating a passive skill tree build...")
    build_focus = "Damage" # Keep default or make configurable later
    logger.info(f"Selected build focus: {build_focus}")

    primary_skill = select_skill_gem(skill_gems, build_focus)
    if not primary_skill:
        logger.warning(f"Could not find a primary skill gem for focus '{build_focus}'. Selecting first available.")
        primary_skill = skill_gems[0] if skill_gems else None

    support_gems_selected = []
    key_stats = []
    if primary_skill:
        support_gems_selected = select_support_gems(support_gems, primary_skill)
        logger.info(f"Selected primary skill: {primary_skill['name']}")
        logger.info(f"Selected support gems: {[s['name'] for s in support_gems_selected]}")
        if build_focus in FOCUS_KEYWORDS:
            key_stats = FOCUS_KEYWORDS[build_focus]
            logger.info(f"Using key stats based on build focus '{build_focus}': {len(key_stats)} keywords found.")
        else:
            logger.warning(f"Build focus '{build_focus}' not found in FOCUS_KEYWORDS. Falling back to skill gem tags.")
            key_stats = identify_key_stats(primary_skill)
    else:
        logger.error("No skill gems available to select a primary skill. Cannot determine key stats.")

    all_node_scores = {}
    logger.info("Scoring all potential target nodes...")
    for node_id, node in nodes.items():
        if node.get("class_start_index"): continue # Skip start nodes
        score = score_node(node, key_stats)
        all_node_scores[node_id] = score
    logger.info(f"Scored {len(all_node_scores)} potential nodes.")

    filtered_scored_nodes = []
    logger.info("Filtering out Ascendancy nodes and nodes with score <= 0...")
    for node_id, score in all_node_scores.items():
        node = nodes[node_id]
        # Ensure 'ascendancy_name' key exists or handle KeyError
        if not node.get("ascendancy_name") and score > 0:
             filtered_scored_nodes.append((score, node_id))

    filtered_scored_nodes.sort(reverse=True)
    logger.info(f"Found {len(filtered_scored_nodes)} valid, non-ascendancy target nodes after filtering.")
    target_nodes = [node_id for _, node_id in filtered_scored_nodes[:5]] # Top 5
    logger.info(f"Top 5 Target nodes: {target_nodes}")

    start_node_id = class_start_nodes.get("Mercenary") # Default class
    if not start_node_id:
        # Fallback if Mercenary start node not found
        start_node_id = list(class_start_nodes.values())[0] if class_start_nodes else list(nodes.keys())[0]
        logger.warning(f"Mercenary start node not found. Using fallback node ID: {start_node_id}")

    # Ensure connections values are sets for find_path if needed, or adapt find_path
    # Assuming find_path expects dict[str, set[str]] or similar
    path_connections = {k: set(v) for k, v in connections.items()} if connections else {}

    # --- Save Build Details ---
    try:
        with open(BUILD_OUTPUT_FILE, "w", encoding="utf-8") as f:
            f.write("### Build: Mercenary Grenade Fire ###\n") # Example name
            f.write("Character Class: Mercenary\n\n")
            f.write("### Selected Gems ###\n")
            if primary_skill:
                f.write(f"Primary Skill Gem: {primary_skill.get('name', 'N/A')} (Level: {primary_skill.get('level', 'N/A')})\n")
                f.write(f"Tags: {primary_skill.get('tags', 'N/A')}\n")
                f.write(f"Description: {primary_skill.get('description', 'N/A')}\n\n")
            else:
                f.write("Primary Skill Gem: None selected\n\n")
            f.write("Support Gems:\n")
            if support_gems_selected:
                for support in support_gems_selected:
                    f.write(f"- {support.get('name', 'N/A')} (Level: {support.get('level', 'N/A')})\n")
                    f.write(f"  Tags: {support.get('tags', 'N/A')}\n")
                    f.write(f"  Description: {support.get('description', 'N/A')}\n")
            else:
                f.write("- None selected\n")
            f.write("\n### Passive Skill Tree Path ###\n")
            f.write(f"Total Points Spent: {len(path)}\n")
            for node_id in path:
                node = nodes.get(node_id, {})
                # Use original connections dict for display if needed
                node_connections = connections.get(node_id, [])
                f.write(f"Node ID: {node_id} | Name: {node.get('name', 'Unknown')} | Stats: {node.get('stats', [])} | X: {node.get('x', 'N/A')} | Y: {node.get('y', 'N/A')} | Connections: {node_connections}\n")
        logger.info(f"Build generated and saved to {BUILD_OUTPUT_FILE}")
    except Exception as e:
        logger.error(f"Error writing build file {BUILD_OUTPUT_FILE}: {e}")

    # --- Generate LLM Data ---
    try:
        # Assuming generate_llm_build_data is synchronous for now
        generate_llm_build_data(skill_gems, support_gems, nodes, connections, class_start_nodes, path, build_focus, "Mercenary")
        logger.info("LLM build data generation initiated.")
    except Exception as e:
        logger.error(f"Error generating LLM build data: {e}")

    # --- (Loading of nodes/connections moved to the top of the function) ---
    # --- Generate HTML Visualization ---
    try:
        # Use the constant HTML_OUTPUT_FILE for the base name
        html_filename_base = os.path.splitext(HTML_OUTPUT_FILE)[0] # Get path without extension
        # Pass the loaded data instead of the fetched data
        # Use the variables loaded from files at the start of the function
        # Pass an empty list for the path as find_path is removed
        generate_interactive_skill_tree(nodes, connections, [], html_filename_base, passive_data)
        logger.info(f"Interactive skill tree HTML generated ({HTML_OUTPUT_FILE}).")
    except Exception as e:
        logger.error(f"Error generating interactive skill tree HTML: {e}")

    logger.info("--- HTML Visualizer Generation Finished ---")


def start_server_and_open_browser():
    """Starts the Flask server and opens the HTML visualizer in a browser."""
    logger.info("--- Starting Flask Server and Opening Browser ---")
    html_file_path = os.path.abspath(HTML_OUTPUT_FILE) # Use absolute path for browser

    # Check if HTML file exists before proceeding
    if not os.path.exists(html_file_path):
        logger.error(f"HTML file not found at {html_file_path}. Cannot open in browser. Please run with --generate first.")
        return # Stop if HTML is missing

    # Start Flask server (server.py) in a separate process
    try:
        logger.info("Starting Flask server (server.py)...")
        # Use sys.executable to ensure the correct python interpreter is used
        server_process = subprocess.Popen([sys.executable, 'server.py'])
        logger.info(f"Flask server started with PID: {server_process.pid}")
        time.sleep(2) # Add a delay to allow the server to initialize
        # We don't wait for the server, just launch it
    except FileNotFoundError:
        logger.error("Error: 'server.py' not found in the current directory.")
        return # Stop if server script is missing
    except Exception as e:
        logger.error(f"Error starting Flask server: {e}")
        return # Stop if server fails to start

    # Open the HTML file in the default web browser
    try:
        logger.info(f"Opening {html_file_path} in web browser...")
        webbrowser.open('http://localhost:5001/') # Open the Flask server URL
        logger.info("Browser should be opening the file.")
    except Exception as e:
        logger.error(f"Error opening HTML file in browser: {e}")

    logger.info("--- Server Start and Browser Open Finished ---")
    logger.info("Note: The Flask server is running in the background. Stop it manually (e.g., Ctrl+C in its terminal if visible, or task manager).")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="PoE2 Skill Tree Analyzer and Visualizer")
    parser.add_argument(
        "--fetch",
        action="store_true",
        help="Fetch/scrape data (gems, passives, items) and rebuild ChromaDB databases."
    )
    parser.add_argument(
        "--generate",
        action="store_true",
        help="Generate the interactive HTML skill tree file based on fetched/existing data."
    )
    parser.add_argument(
        "--run",
        action="store_true",
        help="Run the Flask server and open the generated HTML file in a browser."
    )

    args = parser.parse_args()

    # Determine default behavior
    run_generate = args.generate
    run_run = args.run
    if not (args.fetch or args.generate or args.run):
        logger.info("No arguments provided. Defaulting to --generate and --run.")
        run_generate = True
        run_run = True

    # Execute actions based on flags
    if args.fetch:
        asyncio.run(fetch_data_and_build_db())

    if run_generate:
        # generate_html_visualizer fetches its own data currently
        asyncio.run(generate_html_visualizer())

    if run_run:
        # Check if HTML file exists before trying to run server/browser
        if os.path.exists(HTML_OUTPUT_FILE):
             start_server_and_open_browser()
        else:
             logger.error(f"Cannot run: HTML file '{HTML_OUTPUT_FILE}' not found. Please run with --generate first.")

    logger.info("main.py script finished.")