### Build: Mercenary Grenade Fire ###
Character Class: Mercenary

### Selected Gems ###
Primary Skill Gem: Ancestral Warrior Totem (Level: 13)
Tags: Attack, Totem, AoE, Melee, Physical, Duration, Meta
Description: Description(s):
Raise a
Totem
that uses socketed
Mace
Skills. Cannot use
Channelling
Skills or Skills with Cooldowns.
---
Raises a
Totem
that uses socketed
Mace
Skills. Cannot use
Channelling
Skills or Skills with Cooldowns.
---
Raise a
Totem
that uses socketed
Mace
Skills. Cannot use
Channelling
Skills or Skills with Cooldowns.

Explicit Mods:
- Totemduration is  seconds
- Totemduration is8seconds
- Limit1Totem
- TotembaseCritical Hitchance is5%
- Totembase attack time is1second
- Totemuses its own Two HandedMace, dealing(6–159)to(9–238)basePhysicaldamage
- Totemduration is8seconds
- Limit1Totem
- Totembase attack time is1second
- Totemuses its own weapon, dealing(6–141)to(9–212)basePhysicaldamage
- Totemduration is  seconds
- Totemduration is8seconds
- Limit1Totem
- TotembaseCritical Hitchance is5%
- Totembase attack time is1second
- Totemuses its own Two HandedMace, dealing(6–159)to(9–238)basePhysicaldamage
- active skill has alt attack container [1]
- alt attack container main hand base maximum attack distance [13]
- alt attack container main hand unarmed overide two handed mace type [1]
- ancestral spirit base lockout time ms [600]
- base skill summons totems [1]
- base totem range [60]
- display statset no hit damage [1]
- is totem [1]
- skill is deploy skill [1]
- totem has ancestral warrior spirit [1]
- base skill is totemified [1]
- skill disabled unless cloned [2]

Support Gems:
- Aftershock (Level: 2)
  Tags: Support, Attack, Melee, Slam
  Description: Description(s):
Supports
Slams
you use yourself, giving them a chance to create an
Aftershock
.
---
Supports
Slams
you use yourself, giving them a chance to create an
Aftershock
.
---
Supports
Slams
you use yourself, giving them a chance to create an
Aftershock
.

Explicit Mods:
- Supported skills have25% chance to cause anAftershock
- Supported skills have25% chance to cause anAftershock
- Supported skills have25% chance to cause anAftershock
- (80–120)% increasedPhysicalDamage
- +(10–15)toStrength
- Gain(10–20)Life per Enemy Killed
- SlamSkills you use yourself causeAftershocks
- Alignment (Level: 3)
  Tags: Support, Attack, Projectile
  Description: Description(s):
Supports
Bow
Attacks
. Supported Skills indicate one of four directions, changing indicated direction when an
Attack
with Supported Skill matches that direction.
Attacks
from Supported Skills which match the indicated direction deal much more Damage. Cannot Support
Channelled
Skills.
---
Supports
Bow
Attacks
. Supported Skills indicate one of four directions, changing indicated direction when an
Attack
with Supported Skill matches that direction.
Attacks
from Supported Skills which match the indicated direction deal much more Damage. Cannot Support
Channelled
Skills.

Explicit Mods:
- Supported Skills indicate one of four directionsIndicated direction changes when anAttackwith Supported Skill matches indicated direction
- Supported Skills deal30% moreProjectileDamage whenAttackingin indicated direction
- Supported Skills indicate one of four directionsIndicated direction changes when anAttackwith Supported Skill matches indicated direction
- Supported Skills deal30% moreProjectileDamage whenAttackingin indicated direction
- called shot aiming delay ms [1000]
- called shot angle allowance degs [40]
- Ancestral Aid (Level: 2)
  Tags: Support, Attack, Melee, Strike, Conditional
  Description: Description(s):
Supports
Strikes
you use yourself. Supported Skills cannot be used until you have
Blocked
a certain number of
Hits
, but will be
Ancestrally Boosted
when used. After use, this
Blocked Hit
counter will reset. Cannot Support
Channelled
Skills or Skills which already have a
Condition
for use.
---
Supports
Strikes
you use yourself. Supported Skills cannot be used until you have
Blocked
a certain number of
Hits
, but will be
Ancestrally Boosted
when used. After use, this
Blocked Hit
counter will reset. Cannot Support
Channelled
Skills or Skills which already have a
Condition
for use.

Explicit Mods:
- SupportedStrikesareAncestrally Boosted
- Supported Skills cannot be used until you haveBlocked3Hits
- SupportedStrikesareAncestrally Boosted
- Supported Skills cannot be used until you haveBlocked3Hits

### Passive Skill Tree Path ###
