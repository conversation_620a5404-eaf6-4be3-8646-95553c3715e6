# scrape_poe2_items.py
import asyncio
import httpx # Use httpx for async requests
from bs4 import BeautifulSoup, Tag
import json
import os
# import time # Replaced with asyncio.sleep
from urllib.parse import urljoin
import logging
from random_user_agent.user_agent import UserAgent
from random_user_agent.params import SoftwareName, OperatingSystem
import re

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

BASE_URL = "https://poe2db.tw"
ITEMS_URL = urljoin(BASE_URL, "/us/Items")
OUTPUT_DIR = "data"
OUTPUT_FILE = os.path.join(OUTPUT_DIR, "poe2_items_raw.json")
REQUEST_TIMEOUT = 30 # Increased timeout for async client
REQUEST_DELAY = 0.2 # Delay between *category* page requests (detail pages handled by semaphore)
MAX_CONCURRENT_DETAILS = 50 # Max concurrent detail page fetches
# HEADERS constant removed, User-Agent is now set per-request

# Setup User-Agent rotator
software_names = [SoftwareName.CHROME.value]
operating_systems = [OperatingSystem.WINDOWS.value, OperatingSystem.MACOS.value]
user_agent_rotator = UserAgent(software_names=software_names, operating_systems=operating_systems, limit=100)

# --- Parsing Helper Functions (Remain Synchronous) ---
def parse_stats(stats_container):
    """Parses implicit and explicit stats from a container element (div or td)."""
    if not stats_container:
        return [], []
    implicits = []
    explicits = []
    implicit_divs = stats_container.find_all(['div', 'span'], class_='implicitMod', recursive=True) # Search recursively now
    explicit_divs = stats_container.find_all(['div', 'span'], class_='explicitMod', recursive=True) # Search recursively now

    is_implicit_container = isinstance(stats_container, Tag) and 'implicitMod' in stats_container.get('class', [])
    is_explicit_container = isinstance(stats_container, Tag) and 'explicitMod' in stats_container.get('class', [])

    processed_elements = set() # Keep track of elements processed via specific classes

    for mod in implicit_divs:
        implicits.append(" ".join(mod.stripped_strings))
        processed_elements.add(mod)
        # Also add parents if they were the source to avoid double counting in fallback
        if mod.parent and mod.parent != stats_container:
             processed_elements.add(mod.parent)

    for mod in explicit_divs:
        explicits.append(" ".join(mod.stripped_strings))
        processed_elements.add(mod)
        if mod.parent and mod.parent != stats_container:
             processed_elements.add(mod.parent)

    # Handle container itself having the class, only if not already processed via children
    if is_implicit_container and stats_container not in processed_elements:
         implicits.append(" ".join(stats_container.stripped_strings))
         processed_elements.add(stats_container)
    elif is_explicit_container and stats_container not in processed_elements:
         explicits.append(" ".join(stats_container.stripped_strings))
         processed_elements.add(stats_container)

    # Fallback: Iterate through direct text nodes or unrecognized tags if no specific classes were found
    # This is less reliable but catches stats directly in table cells sometimes
    if not implicit_divs and not explicit_divs and not is_implicit_container and not is_explicit_container:
        logging.debug("No specific stat classes found in container/children. Using stripped_strings fallback.")
        lines = [line.strip() for line in stats_container.stripped_strings if line.strip()]
        # Assume all lines are explicit in this fallback. More complex logic could try to guess.
        explicits.extend(lines)


    implicits = [s for s in implicits if s]
    explicits = [s for s in explicits if s]
    return implicits, explicits


def parse_requirements(req_container):
    """Parses Level, Str, Dex, Int requirements from a container element (div or td)."""
    if not req_container:
        return None, None, None, None

    level, str_req, dex_req, int_req = None, None, None, None
    text_content = req_container.get_text(separator=' ', strip=True)
    # Use regex for more robust parsing
    level_match = re.search(r'Level\s*:\s*(\d+)', text_content, re.IGNORECASE)
    str_match = re.search(r'Str\s*:\s*(\d+)', text_content, re.IGNORECASE)
    dex_match = re.search(r'Dex\s*:\s*(\d+)', text_content, re.IGNORECASE)
    int_match = re.search(r'Int\s*:\s*(\d+)', text_content, re.IGNORECASE)

    try:
        if level_match: level = int(level_match.group(1))
        if str_match: str_req = int(str_match.group(1))
        if dex_match: dex_req = int(dex_match.group(1))
        if int_match: int_req = int(int_match.group(1))
    except ValueError:
        logging.debug(f"Could not parse requirements value as int from: '{text_content}'")
        pass # Ignore parsing errors

    return level, str_req, dex_req, int_req

# --- Async Fetching Functions ---
# Constants for retry logic
MAX_RETRIES = 3
BASE_BACKOFF_DELAY = 1.0  # seconds
RETRYABLE_STATUS_CODES = {408, 429, 500, 502, 503, 504}

async def fetch_with_retry(client: httpx.AsyncClient, url: str, logger: logging.Logger, item_name: str = "Unknown Item", timeout: int = 20) -> httpx.Response | None:
    """
    Fetches a URL with retries on specific errors using exponential backoff.

    Args:
        client: The httpx.AsyncClient instance.
        url: The URL to fetch.
        logger: The logger instance to use for messages.
        item_name: A descriptive name for the item being fetched (for logging).
        timeout: Request timeout in seconds.

    Returns:
        The httpx.Response object on success, or None if all retries fail.
    """
    last_exception = None
    last_status_code = None
    for attempt in range(MAX_RETRIES):
        try:
            logger.debug(f"Attempt {attempt + 1}/{MAX_RETRIES} to fetch {item_name} from {url}")
            # Select a random User-Agent for this specific request
            random_user_agent = user_agent_rotator.get_random_user_agent()
            request_headers = {"User-Agent": random_user_agent}
            logger.debug(f"Using User-Agent: {random_user_agent}")
            # Pass follow_redirects=True, matching the client's configuration, and per-request headers
            response = await client.get(url, headers=request_headers, timeout=timeout, follow_redirects=True)
            response.raise_for_status() # Raise HTTPStatusError for 4xx/5xx responses
            logger.debug(f"Successfully fetched {item_name} from {url} on attempt {attempt + 1}")
            return response # Success

        except httpx.TimeoutException as e:
            logger.warning(f"Timeout fetching {item_name} from {url} (Attempt {attempt + 1}/{MAX_RETRIES}).")
            last_exception = e
            last_status_code = 408 # Treat timeout like a 408 for logging consistency

        except httpx.NetworkError as e:
            # Includes connection errors, DNS errors etc. - generally retryable
            logger.warning(f"Network error fetching {item_name} from {url} (Attempt {attempt + 1}/{MAX_RETRIES}): {e}")
            last_exception = e
            last_status_code = None # No specific status code

        except httpx.HTTPStatusError as e:
            last_exception = e
            last_status_code = e.response.status_code
            if e.response.status_code in RETRYABLE_STATUS_CODES:
                logger.warning(f"HTTP status {e.response.status_code} for {item_name} from {url} (Attempt {attempt + 1}/{MAX_RETRIES}).")
                # Retryable status code, continue to backoff
            else:
                # Non-retryable status code (e.g., 404 Not Found)
                logger.error(f"Non-retryable HTTP status {e.response.status_code} for {item_name} from {url}. Giving up.")
                return None # Indicate permanent failure for non-retryable HTTP errors

        except Exception as e: # Catch any other unexpected errors during the request
             logger.error(f"Unexpected error during fetch attempt {attempt + 1}/{MAX_RETRIES} for {item_name} from {url}: {e}", exc_info=True)
             last_exception = e
             last_status_code = None
             # Decide if truly unexpected errors should be retried; for now, we will retry them.

        # If we are here, an error occurred and it might be retryable
        if attempt < MAX_RETRIES - 1:
            delay = BASE_BACKOFF_DELAY * (2 ** attempt)
            # Add slight jitter? Optional. delay += random.uniform(0, 0.5)
            status_str = f"Status: {last_status_code}" if last_status_code else f"Error: {type(last_exception).__name__}"
            logger.warning(f"Request failed for {item_name} at {url} ({status_str}). Retrying in {delay:.2f}s...")
            await asyncio.sleep(delay)
        else:
            # Max retries reached
            status_str = f"Status: {last_status_code}" if last_status_code else f"Error: {type(last_exception).__name__}"
            logger.error(f"Failed to fetch {item_name} from {url} after {MAX_RETRIES} attempts ({status_str}). Last error: {last_exception}")
            return None # Indicate final failure after all retries

    return None # Should technically be unreachable if loop completes, but safety return



async def get_category_urls(client, main_page_url):
    """Fetches the main items page and extracts category URLs asynchronously."""
    logging.info(f"Fetching main items page: {main_page_url}")
    try:
        response = await client.get(main_page_url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        content_area = soup.find('main') or soup.find('body') or soup
        if not content_area:
             logging.error("Could not find <main> or <body> tag. Searching entire HTML.")
             content_area = soup

        logging.info("Performing broad search for category links in content area.")
        links = content_area.find_all('a', href=lambda href: href and href.startswith('/us/') and href != '/us/Items')

        category_links = []
        for link in links:
            full_url = urljoin(BASE_URL, link['href'])
            # Filter list adjusted based on previous runs
            if any(keyword in full_url for keyword in ['passive-skill-tree', 'atlas-skill-tree', 'marked', 'patreon', 'pob', 'Ascendancy', 'Modifiers', 'Keywords', 'League', 'Quest', 'Vendor_recipe', 'FlavourText', 'Skill_Gems', 'Support_Gems', 'Characters', 'Crafting', 'Pantheon']):
                logging.debug(f"Skipping likely non-item category URL: {full_url}")
                continue
            category_links.append(full_url)

        category_links = sorted(list(set(category_links)))

        if not category_links:
            logging.warning("Broad search found no category links. Check page structure or if content is dynamic.")
        else:
            logging.info(f"Found {len(category_links)} unique potential category URLs via broad search.")

        return category_links

    except httpx.TimeoutException:
        logging.error(f"Timeout error fetching {main_page_url}")
        return []
    except httpx.RequestError as e:
        logging.error(f"Request error fetching {main_page_url}: {e}")
        return []
    except Exception as e:
        logging.error(f"Error parsing {main_page_url}: {e}", exc_info=True)
        return []


async def fetch_and_process_item_detail(client, detail_url, item_name, semaphore):
    """Fetches and parses a single item's detail page asynchronously."""
    async with semaphore: # Limit concurrency
        logging.debug(f"Acquired semaphore for '{item_name}', fetching from {detail_url}...")
        # await asyncio.sleep(0.05) # Optional small delay *inside* semaphore
        # Use the retry wrapper
        detail_response = await fetch_with_retry(client, detail_url, logging, item_name=item_name, timeout=REQUEST_TIMEOUT)

        if detail_response is None:
            # Fetch failed permanently after retries
            logging.warning(f"Permanent failure fetching details for {item_name} from {detail_url}. Cannot process.")
            return {'detailed_description': "Failed to fetch details after retries.", 'error': 'FetchFailed', 'implicit_stats': [], 'explicit_stats': [], 'level_req': None, 'str_req': None, 'dex_req': None, 'int_req': None}

        # --- Process successful response ---
        try:
            # No need to call raise_for_status() again, fetch_with_retry handles non-retryable HTTP errors
            detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
            logging.debug(f"Successfully fetched details for '{item_name}'.")

            # Extract main description
            desc_elem = detail_soup.find("div", class_="secDescrText")
            scraped_description = desc_elem.get_text(separator="\n", strip=True) if desc_elem else "No description found."

            # Extract stats and requirements from detail page
            implicit_stats, explicit_stats = [], []
            level_req, str_req, dex_req, int_req = None, None, None, None

            item_box = detail_soup.find('div', class_='itemBoxContent') or detail_soup
            if item_box:
                implicit_stats, explicit_stats = parse_stats(item_box)
                req_box = detail_soup.find('div', class_='requirements') or item_box
                if req_box:
                    level_req, str_req, dex_req, int_req = parse_requirements(req_box)
                else:
                     logging.debug(f"Could not find requirements div on detail page: {detail_url}")
            else:
                logging.warning(f"Could not find itemBoxContent on detail page: {detail_url}")

            # Combine description and stats
            combined_desc = f"Description:\n{scraped_description}"
            if implicit_stats:
                combined_desc += "\n\nImplicit Mods:\n- " + "\n- ".join(implicit_stats)
            if explicit_stats:
                combined_desc += "\n\nExplicit Mods:\n- " + "\n- ".join(explicit_stats)

            logging.debug(f"Successfully processed details for '{item_name}'.")
            return {
                'detailed_description': combined_desc,
                'implicit_stats': implicit_stats,
                'explicit_stats': explicit_stats,
                'level_req': level_req,
                'str_req': str_req,
                'dex_req': dex_req,
                'int_req': int_req,
                'error': None # Indicate success
            }

        # Catch parsing errors or other unexpected issues after successful fetch
        except Exception as e:
            logging.error(f"Error processing successfully fetched detail page {detail_url} for {item_name}: {e}", exc_info=False)
            # Return error but potentially keep any partially parsed data if needed?
            # For now, return a clear error state.
            return {'detailed_description': f"Failed to process successfully fetched details (Error: {e}).", 'error': 'ProcessingError', 'implicit_stats': [], 'explicit_stats': [], 'level_req': None, 'str_req': None, 'dex_req': None, 'int_req': None}


async def scrape_category_page(client, category_url, semaphore):
    """Scrapes item data from a single category page asynchronously, fetching details concurrently."""
    logging.info(f"Scraping category: {category_url}")
    items_summary = [] # Store summary data before fetching details
    tasks = []

    try:
        response = await client.get(category_url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        content_area = soup.find('div', class_='tab-pane active') or soup.find('div', class_='tab-content') or soup.find('body') or soup
        if not content_area:
             logging.error(f"Could not find main content area on {category_url}. Skipping.")
             return []

        # --- Strategy 1: Div-based layout ---
        item_divs = content_area.select('div.row > div.col > div.d-flex.border.rounded')
        if item_divs:
            logging.info(f"Found {len(item_divs)} items using div-based layout.")
            for item_container in item_divs:
                item_data = { # Store summary data first
                    'category_url': category_url, 'image_url': None, 'name': "Unknown",
                    'base_type': "Unknown", 'detail_page_url': None,
                    # Placeholders for data to be fetched/overwritten by detail task
                    'implicit_stats': [], 'explicit_stats': [],
                    'level_req': None, 'str_req': None, 'dex_req': None, 'int_req': None,
                    'detailed_description': "Not fetched.",
                }
                try:
                    img_tag = item_container.find('img', class_='panel-item-icon')
                    if img_tag and 'src' in img_tag.attrs:
                         img_src = img_tag['src']
                         if img_src.startswith('//'): item_data['image_url'] = 'https:' + img_src
                         elif img_src.startswith('/'): item_data['image_url'] = urljoin(BASE_URL, img_src)
                         else: item_data['image_url'] = img_src

                    info_div = item_container.find('div', class_='flex-grow-1')
                    if not info_div: continue

                    name_link = info_div.find('a')
                    full_name_text = ""
                    if name_link:
                        full_name_text = name_link.text.strip()
                        if 'href' in name_link.attrs:
                            item_data['detail_page_url'] = urljoin(BASE_URL, name_link['href'])
                    else:
                        first_div = info_div.find('div')
                        if first_div:
                            link_in_div = first_div.find('a')
                            full_name_text = link_in_div.text.strip() if link_in_div else first_div.text.strip()

                    if full_name_text:
                        item_data['name'] = full_name_text
                        base_type_div = info_div.find('div', class_='item_basetype')
                        if base_type_div:
                             item_data['base_type'] = base_type_div.text.strip()
                             if item_data['name'].endswith(item_data['base_type']):
                                 item_data['name'] = item_data['name'][:-len(item_data['base_type'])].strip()
                             if not item_data['name']: item_data['name'] = item_data['base_type']
                        else:
                             item_data['base_type'] = item_data['name']
                    else:
                        logging.warning(f"Could not extract name text from info_div. Skipping div.")
                        continue # Skip if no name found

                    # Initial parse of stats/reqs from summary (might be overwritten by detail)
                    item_data['implicit_stats'], item_data['explicit_stats'] = parse_stats(info_div)
                    item_data['level_req'], item_data['str_req'], item_data['dex_req'], item_data['int_req'] = parse_requirements(info_div)

                    items_summary.append(item_data) # Add summary data

                except Exception as e:
                    logging.error(f"Error processing summary div item on {category_url}: {e}", exc_info=False)
                    continue

        # --- Strategy 2: Table-based layout (if no divs found/processed) ---
        if not items_summary: # Only check tables if divs didn't yield results
            logging.info(f"No div-based items found or processed on {category_url}. Checking for tables...")
            item_tables = content_area.find_all('table')
            if item_tables:
                logging.info(f"Found {len(item_tables)} table(s) on {category_url}.")
                for table_index, item_table in enumerate(item_tables):
                    rows = item_table.find('tbody').find_all('tr') if item_table.find('tbody') else item_table.find_all('tr')
                    if not rows: continue
                    if rows[0].find('th'): rows = rows[1:] # Skip header

                    logging.info(f"Table {table_index+1}: Found {len(rows)} potential item rows.")
                    for row_index, row in enumerate(rows):
                        cells = row.find_all(['td', 'th'])
                        if not cells: continue

                        item_data = { # Store summary data first
                            'category_url': category_url, 'image_url': None, 'name': "Unknown",
                            'base_type': "Unknown", 'detail_page_url': None,
                            'implicit_stats': [], 'explicit_stats': [],
                            'level_req': None, 'str_req': None, 'dex_req': None, 'int_req': None,
                            'detailed_description': "Not fetched.",
                        }
                        found_name = False
                        try:
                            for cell_index, cell in enumerate(cells):
                                if not item_data['image_url']:
                                    img_tag = cell.find('img')
                                    if img_tag and 'src' in img_tag.attrs and 'item_currency' not in img_tag.parent.get('class', []):
                                         img_src = img_tag['src']
                                         if img_src.startswith('//'): item_data['image_url'] = 'https:' + img_src
                                         elif img_src.startswith('/'): item_data['image_url'] = urljoin(BASE_URL, img_src)
                                         else: item_data['image_url'] = img_src

                                if not found_name:
                                    name_link = cell.find('a', class_=lambda c: c != 'item_currency' if c else True)
                                    if name_link:
                                        item_data['name'] = name_link.text.strip()
                                        if 'href' in name_link.attrs:
                                            item_data['detail_page_url'] = urljoin(BASE_URL, name_link['href'])
                                        base_type_div = cell.find('div', class_='item_basetype') or row.find('div', class_='item_basetype')
                                        item_data['base_type'] = base_type_div.text.strip() if base_type_div else item_data['name']
                                        found_name = True

                                # Initial parse of stats/reqs from summary (might be overwritten by detail)
                                if cell.find(['div', 'span'], class_=['implicitMod', 'explicitMod']):
                                    imp_s, exp_s = parse_stats(cell)
                                    if imp_s: item_data['implicit_stats'].extend(imp_s)
                                    if exp_s: item_data['explicit_stats'].extend(exp_s)

                                lvl, str_r, dex_r, int_r = parse_requirements(cell)
                                if lvl is not None: item_data['level_req'] = lvl
                                if str_r is not None: item_data['str_req'] = str_r
                                if dex_r is not None: item_data['dex_req'] = dex_r
                                if int_r is not None: item_data['int_req'] = int_r

                            if found_name and item_data['name'] != "Unknown":
                                # Deduplicate stats parsed from multiple cells if necessary
                                item_data['implicit_stats'] = sorted(list(set(item_data['implicit_stats'])))
                                item_data['explicit_stats'] = sorted(list(set(item_data['explicit_stats'])))
                                items_summary.append(item_data)
                            elif not found_name:
                                logging.warning(f"Skipped table row {row_index+1}: Could not parse name.")

                        except Exception as e:
                            logging.error(f"Error processing summary table row {row_index+1} on {category_url}: {e}", exc_info=False)
                            continue
            else:
                 logging.warning(f"No tables found on {category_url} either.")


        # --- Create and Gather Detail Fetching Tasks ---
        final_items = []
        detail_tasks_info = [] # Store mapping from task to original summary index

        for idx, summary_data in enumerate(items_summary):
            if summary_data['detail_page_url']:
                task = asyncio.create_task(
                    fetch_and_process_item_detail(client, summary_data['detail_page_url'], summary_data['name'], semaphore)
                )
                detail_tasks_info.append({'task': task, 'summary_index': idx})
            else:
                # If no detail URL, add summary data directly with appropriate description
                logging.debug(f"No detail page URL found for item: {summary_data['name']}")
                summary_data['detailed_description'] = "No detail page link found. Stats from summary page."
                # Ensure stats are lists of strings
                summary_data['implicit_stats'] = [str(s) for s in summary_data.get('implicit_stats', [])]
                summary_data['explicit_stats'] = [str(s) for s in summary_data.get('explicit_stats', [])]
                final_items.append(summary_data)

        if detail_tasks_info:
            logging.info(f"Waiting for {len(detail_tasks_info)} detail fetching tasks for {category_url}...")
            results = await asyncio.gather(*(t['task'] for t in detail_tasks_info), return_exceptions=True)
            logging.info(f"Finished detail fetching tasks for {category_url}.")

            # Process results
            for i, result in enumerate(results):
                summary_index = detail_tasks_info[i]['summary_index']
                original_item_data = items_summary[summary_index]

                if isinstance(result, Exception):
                    logging.error(f"Task for {original_item_data['name']} failed: {result}", exc_info=result)
                    original_item_data['detailed_description'] = f"Failed to fetch details ({type(result).__name__}). Stats from summary page."
                    # Keep summary stats/reqs
                elif isinstance(result, dict) and result.get('error'):
                    # Handle errors reported by fetch_and_process_item_detail
                    original_item_data['detailed_description'] = result['detailed_description']
                    # Keep summary stats/reqs
                elif isinstance(result, dict):
                    # Success - Update item data with details
                    original_item_data['detailed_description'] = result['detailed_description']
                    # Overwrite stats/reqs only if detail fetch was successful and returned values
                    # Check if detail parsing actually found stats (don't overwrite with empty lists if summary had stats)
                    if result['implicit_stats'] or result['explicit_stats']:
                         original_item_data['implicit_stats'] = result['implicit_stats']
                         original_item_data['explicit_stats'] = result['explicit_stats']
                    # Overwrite reqs only if they are not None in the result
                    if result['level_req'] is not None: original_item_data['level_req'] = result['level_req']
                    if result['str_req'] is not None: original_item_data['str_req'] = result['str_req']
                    if result['dex_req'] is not None: original_item_data['dex_req'] = result['dex_req']
                    if result['int_req'] is not None: original_item_data['int_req'] = result['int_req']
                else:
                     logging.error(f"Task for {original_item_data['name']} returned unexpected result type: {type(result)}")
                     original_item_data['detailed_description'] = "Failed to process details (Unexpected Result). Stats from summary page."

                # Ensure stats are lists of strings before adding to final list
                original_item_data['implicit_stats'] = [str(s) for s in original_item_data.get('implicit_stats', [])]
                original_item_data['explicit_stats'] = [str(s) for s in original_item_data.get('explicit_stats', [])]
                final_items.append(original_item_data)

        logging.info(f"Finished processing category {category_url}. Found {len(final_items)} items.")
        return final_items

    except httpx.TimeoutException:
        logging.error(f"Timeout error fetching category page {category_url}")
        return []
    except httpx.RequestError as e:
        logging.error(f"Request error fetching category page {category_url}: {e}")
        return []
    except Exception as e:
        logging.error(f"Error parsing category page {category_url}: {e}", exc_info=True)
        return []


async def scrape_and_save_items():
    """Scrapes item data asynchronously and saves it to a JSON file."""
    logging.info("Starting PoE 2 Item Scraper (Async)...")

    if not os.path.exists(OUTPUT_DIR):
        try:
            os.makedirs(OUTPUT_DIR)
            logging.info(f"Created output directory: {OUTPUT_DIR}")
        except OSError as e:
            logging.error(f"Failed to create output directory {OUTPUT_DIR}: {e}")
            return

    all_items = []
    # Create client and semaphore once
    # Removed headers=HEADERS from client initialization
    # Removed headers=HEADERS from client initialization
    async with httpx.AsyncClient(follow_redirects=True, timeout=REQUEST_TIMEOUT) as client:
        category_urls = await get_category_urls(client, ITEMS_URL)
        if not category_urls:
            logging.error("No category URLs found. Exiting.")
            return

        logging.info(f"Found {len(category_urls)} categories to scrape.")
        logging.debug(f"Categories: {category_urls}")

        semaphore = asyncio.Semaphore(MAX_CONCURRENT_DETAILS)

        for url in category_urls:
            items_on_page = await scrape_category_page(client, url, semaphore)
            if items_on_page:
                all_items.extend(items_on_page)
            logging.info(f"Finished scraping {url}. Total items so far: {len(all_items)}")
            logging.debug(f"Waiting for {REQUEST_DELAY} seconds before next category...")
            await asyncio.sleep(REQUEST_DELAY) # Use asyncio.sleep

    logging.info(f"Total items scraped across all categories: {len(all_items)}")

    if all_items:
        logging.info(f"Attempting to save {len(all_items)} items to {OUTPUT_FILE}")
        try:
            # Final check: Ensure stats are lists of strings (should be handled earlier, but belt-and-suspenders)
            for item in all_items:
                 item['implicit_stats'] = [str(s) for s in item.get('implicit_stats', [])]
                 item['explicit_stats'] = [str(s) for s in item.get('explicit_stats', [])]

            with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
                json.dump(all_items, f, indent=2, ensure_ascii=False)
            logging.info(f"Successfully saved data to {OUTPUT_FILE}")
        except IOError as e:
            logging.error(f"Error writing to file {OUTPUT_FILE}: {e}")
        except Exception as e:
            logging.error(f"An unexpected error occurred during file writing: {e}", exc_info=True)
    else:
        logging.warning("No items were scraped, skipping file save.")

    logging.info("Async scraping process finished.")


async def main():
    """Async main function to orchestrate the scraping process."""
    await scrape_and_save_items()

if __name__ == "__main__":
    try:
        import httpx # Check for httpx now
        from bs4 import BeautifulSoup
        import asyncio
    except ImportError as e:
        print("---------------------------------------------------------")
        print(f"Error: Missing required library: {e.name}")
        print("Please install 'httpx' and 'beautifulsoup4'.")
        print("You can install them using: pip install httpx beautifulsoup4")
        print("---------------------------------------------------------")
        exit(1)

    asyncio.run(main()) # Run the async main function