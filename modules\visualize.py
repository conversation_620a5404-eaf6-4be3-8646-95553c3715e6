# modules/visualize.py
import json
import html # Import the html module for escaping
import logging

logger = logging.getLogger(__name__)
# get_connect_id function remains the same
def get_connect_id(node1_id, node2_id):
    node1_id = int(node1_id)
    node2_id = int(node2_id)
    if node1_id > node2_id:
        node1_id, node2_id = node2_id, node1_id
    return f"c{node1_id}-{node2_id}"

def generate_interactive_skill_tree(nodes_data_arg, connections_data_arg, path, filename, passive_data):
    # Use the passed-in arguments
    nodes = nodes_data_arg
    connections = connections_data_arg
    # --- Data Preparation (remains mostly the same) ---
    min_x = float("inf")
    max_x = float("-inf")
    min_y = float("inf")
    max_y = float("-inf")
    for node in nodes.values():
        if "x" not in node or "y" not in node:
            continue
        min_x = min(min_x, node["x"])
        max_x = max(max_x, node["x"])
        min_y = min(min_y, node["y"])
        max_y = max(max_y, node["y"])
    tree_width = max_x - min_x if max_x > min_x else 0
    tree_height = max_y - min_y if max_y > min_y else 0
    initial_center_x = (min_x + max_x) / 2 if max_x > min_x else 0
    initial_center_y = (min_y + max_y) / 2 if max_y > min_y else 0

    nodes_data = {}
    for node_id, node in nodes.items():
        if "x" not in node or "y" not in node or node.get("is_mastery", False): # Added .get for safety
            continue
        nodes_data[node_id] = {
            "x": node["x"],
            "y": node["y"],
            "name": node.get("name", "Unknown"), # Added .get
            "stats": node.get("stats", ""),      # Added .get
            "is_notable": node.get("is_notable", False), # Added .get
            "is_keystone": node.get("is_keystone", False),# Added .get
            "group": node.get("group"),
            "orbit": node.get("orbit"),
            "orbitIndex": node.get("orbitIndex")
        }

    connections_data = []
    drawn_connections = set()
    # Ensure connections is a dictionary before iterating
    if isinstance(connections, dict):
        for node_id, node in nodes.items():
            if "x" not in node or "y" not in node or node.get("is_mastery", False):
                continue
            # Ensure node_id exists in connections and is iterable
            if node_id in connections and hasattr(connections[node_id], '__iter__'):
                for out_id in connections[node_id]:
                    # Ensure out_id exists in nodes and has required properties
                    if out_id not in nodes or "x" not in nodes[out_id] or "y" not in nodes[out_id] or nodes[out_id].get("is_mastery", False):
                        continue
                    connect_id = get_connect_id(node_id, out_id)
                    if connect_id in drawn_connections:
                        continue
                    drawn_connections.add(connect_id)
                    connections_data.append({
                        "from": node_id,
                        "to": out_id,
                        "is_highlighted": node_id in path and out_id in path
                    })

    # Safely get constants
    constants_data = passive_data.get("constants", {}) if isinstance(passive_data, dict) else {}

    # --- Convert data to JSON strings (NO CHANGE HERE) ---
    # Use different variable names to avoid confusion
    nodes_json_string = json.dumps(nodes_data)
    connections_json_string = json.dumps(connections_data)
    path_json_string = json.dumps(path) # path should be a list of node IDs
    constants_json_string = json.dumps(constants_data)

    # --- Simplified HTML Generation ---
    # Embed JSON strings safely and use JSON.parse() in JavaScript
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Path of Exile 2 Skill Tree</title>
    <style>
        body {{ margin: 0; overflow: hidden; font-family: Arial, sans-serif; background-color: #1a1a1a; }} /* Added background */
        #skillTree {{ width: 100vw; height: 100vh; display: block; }} /* Ensure canvas is block */
        #tooltip {{ position: absolute; background: rgba(0, 0, 0, 0.85); color: #eee; padding: 8px 12px; border-radius: 4px; pointer-events: none; display: none; font-size: 0.9em; max-width: 300px; border: 1px solid #444; }}
        #searchContainer {{ position: absolute; top: 10px; left: 10px; background: rgba(0, 0, 0, 0.7); padding: 8px; border-radius: 5px; z-index: 10; }} /* Search container style */
        #searchContainer input {{ padding: 5px; border-radius: 3px; border: 1px solid #555; background-color: #333; color: white; }} /* Search input style */
        #searchContainer button {{ padding: 5px 10px; margin-left: 5px; border-radius: 3px; border: none; background-color: #007bff; color: white; cursor: pointer; }} /* Search button style */
        #searchContainer button:hover {{ background-color: #0056b3; }} /* Search button hover style */
        #statsPanel {{ position: absolute; top: 10px; right: 10px; background: rgba(0, 0, 0, 0.8); color: white; padding: 10px; border-radius: 5px; max-height: 80vh; overflow-y: auto; z-index: 10; width: 250px; font-size: 0.9em; }}
        #statsPanel h3 {{ margin-top: 0; }}
        #statsPanel p {{ margin: 4px 0; }}
        #saveButton {{ position: absolute; bottom: 10px; left: 10px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; z-index: 10; font-size: 1em; }}
        #saveButton:hover {{ background: #0056b3; }}
        #chatInterface {{ position: absolute; bottom: 60px; left: 10px; background: rgba(0,0,0,0.75); padding: 15px; border-radius: 5px; color: white; z-index: 10; display: flex; flex-direction: column; gap: 10px; resize: both; overflow: auto; min-width: 300px; min-height: 250px; width: 350px; height: 300px; }} /* Ensured resize, overflow, min dimensions */
        #chatInterface h4 {{ margin: 0 0 5px 0; }}
        #chatInterface textarea {{ padding: 8px; border-radius: 3px; border: 1px solid #555; background-color: #333; color: white; width: calc(100% - 18px); font-family: inherit; font-size: 0.9em; resize: vertical; min-height: 40px; }} /* Style for textarea */
        #chatInterface button {{ padding: 8px 12px; border-radius: 3px; border: none; background-color: #28a745; color: white; cursor: pointer; }}
        #chatInterface button:hover {{ background-color: #218838; }}
        #llmResponseOutput {{ margin-top: 5px; /* Removed max-height */ overflow-y: auto; border: 1px solid #555; padding: 8px; min-height: 40px; background: rgba(20,20,20,0.9); font-size: 0.9em; white-space: pre-wrap; flex-grow: 1; }} /* Added flex-grow to fill space */
    </style>
</head>
<body>
    <canvas id="skillTree"></canvas>
    <div id="tooltip"></div>
    <div id="statsPanel">
        <h3>Selected Stats</h3>
        <div id="statsDisplay">No stats to display.</div>
    </div>
    <div id="searchContainer">
        <input type="text" id="nodeSearchInput" placeholder="Search node name...">
        <button id="nodeSearchButton">Search</button>
    </div>
    <button id="saveButton">Save Selected Nodes</button>
    <div id="chatInterface">
        <h4>Chat with LLM</h4>
        <textarea id="userQueryInput" rows="3" placeholder="Ask the LLM..."></textarea>
        <input type="text" id="buildGoalInput" placeholder="Enter build goal (e.g., Fire Damage)">
        <div id="actionButtonsContainer" style="margin-top: 5px;">
            <button id="getSuggestionButton">Get Suggestion (Uses Nodes)</button>
            <button id="askGeneralQuestionButton" style="margin-left: 5px; background-color: #17a2b8;">Ask General Question</button>
            <button id="clearChatHistoryButton" style="margin-left: 5px; background-color: #dc3545;">Clear History</button>
        </div>
        <div id="imageUploadContainer" style="margin-top: 10px; border: 1px solid #444; padding: 8px; border-radius: 3px;">
            <input type="file" id="imageUploadInput" accept="image/*" multiple style="border: 1px solid #555; padding: 5px; border-radius: 3px; background-color: #333; color: white;">
            <span id="uploadedFileName" style="font-size: 0.8em; color: #aaa; margin-left: 5px; display: inline-block;">No image selected</span>
            <button id="getSuggestionWithImageButton" style="background-color: #ffc107; color: black; margin-left: 10px;">Get Suggestion with Image</button>
        </div>
        <div id="llmThoughtsContainer" style="display: none; /* Initially hidden */ margin-top: 10px; margin-bottom: 5px; border: 1px solid #555; border-radius: 3px;">
            <button id="toggleThoughtsButton" style="background: none; border: none; color: #aaa; width: 100%; text-align: left; padding: 5px; cursor: pointer;">
                > Thoughts...
            </button>
            <div id="llmThoughtsOutput" style="display: none; /* Initially hidden */ padding: 8px; background: rgba(10,10,10,0.9); font-size: 0.85em; white-space: pre-wrap; max-height: 150px; overflow-y: auto;">
                <!-- Thinking content will go here -->
            </div>
        </div>
        <div id="llmResponseOutput"></div>
    </div>

    <script>
        // Embed data safely as strings and parse in JS
        const skillTreeConfig = {{}};
        try {{
            // Use JSON.stringify again within JS template literals for robust escaping
            skillTreeConfig.nodes = JSON.parse({json.dumps(nodes_json_string)});
            skillTreeConfig.connections = JSON.parse({json.dumps(connections_json_string)});
            skillTreeConfig.path = JSON.parse({json.dumps(path_json_string)});
            skillTreeConfig.constants = JSON.parse({json.dumps(constants_json_string)});
            // Non-JSON data can be embedded directly
            skillTreeConfig.initial_center_x = {initial_center_x};
            skillTreeConfig.initial_center_y = {initial_center_y};
            skillTreeConfig.tree_width = {tree_width};
            skillTreeConfig.tree_height = {tree_height};
        }} catch (e) {{
            console.error("Error parsing embedded skill tree data:", e);
            // Provide fallback empty data to prevent further errors in skill_tree.js
            skillTreeConfig.nodes = {{}};
            skillTreeConfig.connections = [];
            skillTreeConfig.path = [];
            skillTreeConfig.constants = {{}};
            skillTreeConfig.initial_center_x = {initial_center_x};
            skillTreeConfig.initial_center_y = {initial_center_y};
            skillTreeConfig.tree_width = {tree_width};
            skillTreeConfig.tree_height = {tree_height};
        }}

        // initSkillTree (in skill_tree.js) will use this config object
    </script>
    <script src="/static/skill_tree.js" defer></script>

</body>
</html>
"""

    # --- File Writing ---
    html_filename = f"{filename}.html"
    logger.info(f"Attempting to save interactive skill tree HTML to {html_filename}...")
    try:
        with open(html_filename, "w", encoding="utf-8") as f:
            f.write(html_content)
        logger.info(f"Successfully saved interactive skill tree to {html_filename}")
    except (IOError, PermissionError) as e: # Catch specific IO errors
        logger.error(f"Error writing HTML file {html_filename}: {e}", exc_info=True)
    except Exception as e: # Catch any other unexpected errors
        logger.error(f"An unexpected error occurred while writing HTML file {html_filename}: {e}", exc_info=True)