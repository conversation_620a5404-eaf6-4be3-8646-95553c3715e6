import re
# server.py
import os
import json
from pathlib import Path
from flask import Flask, request, jsonify, Response, stream_with_context, send_from_directory
import chromadb
from sentence_transformers import SentenceTransformer
import openai
import logging
from flask_cors import CORS # Added for CORS support
from werkzeug.utils import secure_filename
from modules.llm_integration import get_llm_suggestion_stream, get_llm_suggestion_with_image_stream, query_relevant_items # Added LLM-driven item retrieval
from modules.llm_integration import get_llm_suggestion_stream
import base64 # Added for image encoding
from modules.process_data import lookahead_pathfinding
from modules.process_data import get_adjacent_unallocated_nodes, calculate_accumulated_stats # Added imports
import re # Import regex for level parsing
from modules.gear_processor import process_gear_image  # Import for gear analysis endpoint
# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Initialization ---
UPLOAD_FOLDER = Path("data/uploads/") # Define upload directory
DATA_DIR = Path("data") # Define data directory for serving HTML

# Find the latest ChromaDB directory
latest_chroma_path = None
item_chroma_client = None # Client for data/chroma_db/
build_chroma_client = None # Client for data/chroma_db_build/
item_collection = None # Collection from item_chroma_client
skill_tree_collection = None # Collection for skill tree nodes from build_chroma_client
passives_collection = None # Collection for passive skill descriptions from build_chroma_client
gems_collection = None # Collection for gem descriptions from build_chroma_client
embedder = None
openai_client = None
all_nodes = None
all_connections = None
known_gem_names = set() # Global set to store known gem names


try:
    # --- Connect to Stable ChromaDB Path for Items ---
    stable_chroma_path = Path("data/chroma_db/")
    logger.info(f"Attempting to connect to ChromaDB at stable path: {stable_chroma_path}")
    try:
        if stable_chroma_path.exists() and stable_chroma_path.is_dir():
            item_chroma_client = chromadb.PersistentClient(path=str(stable_chroma_path))
            # Get item collection
            item_collection = item_chroma_client.get_or_create_collection(name="poe2_items") # Use item collection name
            logger.info("Successfully connected to ChromaDB and got 'poe2_items' collection.")
            # Optionally, still try to get skill tree collection if needed elsewhere
            # skill_tree_collection = chroma_client.get_or_create_collection(name="poe2_skill_tree")
            # logger.info("Successfully got 'poe2_skill_tree' collection.")
        else:
             logger.warning(f"Stable ChromaDB path '{stable_chroma_path}' not found. Item suggestions will not work.")
             item_chroma_client = None # Ensure item client is None if path doesn't exist
             item_collection = None
    except Exception as e:
        logger.error(f"Error connecting to ChromaDB at {stable_chroma_path}: {e}")
        item_chroma_client = None
        item_collection = None
        # skill_tree_collection = None


    # --- Connect to ChromaDB Path for Build/Skill Tree Context ---
    build_db_path = Path("data/chroma_db_build/")
    logger.info(f"Attempting to connect to ChromaDB at build path: {build_db_path}")
    try:
        if build_db_path.exists() and build_db_path.is_dir():
            # Always create a separate client for the build DB path
            build_chroma_client = chromadb.PersistentClient(path=str(build_db_path))

            # Get skill tree collection from the build client
            skill_tree_collection = build_chroma_client.get_or_create_collection(
                name="poe2_skill_tree",
                metadata={"hnsw:space": "cosine", "embedding_function": "sentence_transformer"}
            )
            logger.info("Successfully connected to build ChromaDB and got 'poe2_skill_tree' collection.")

            # Get passives collection from the build client
            try:
                passives_collection = build_chroma_client.get_or_create_collection(
                    name="poe2_passives",
                    metadata={"hnsw:space": "cosine", "embedding_function": "sentence_transformer"}
                )
                logger.info("Successfully got 'poe2_passives' collection.")
            except Exception as e_passive:
                logger.error(f"Error getting 'poe2_passives' collection: {e_passive}")
                passives_collection = None

            # Get gems collection from the build client
            try:
                gems_collection = build_chroma_client.get_or_create_collection(
                    name="poe2_gems",
                    metadata={"hnsw:space": "cosine", "embedding_function": "sentence_transformer"}
                )
                logger.info("Successfully got 'poe2_gems' collection.")
            except Exception as e_gem:
                logger.error(f"Error getting 'poe2_gems' collection: {e_gem}")
                gems_collection = None

            # --- Load Known Gem Names ---
            if gems_collection:
                try:
                    logger.info("Attempting to load all gem names from ChromaDB...")
                    all_gem_metadata = gems_collection.get(include=['metadatas'])
                    if all_gem_metadata and 'metadatas' in all_gem_metadata and all_gem_metadata['metadatas']:
                        known_gem_names = set(
                            meta['name'] for meta in all_gem_metadata['metadatas']
                            # Ensure meta is not None, 'name' exists, AND name is not empty string
                            if meta and 'name' in meta and meta['name']
                        )
                        logger.info(f"Successfully loaded {len(known_gem_names)} unique non-empty gem names.")
                    else:
                        logger.warning("Could not retrieve gem metadata or metadata list is empty.")
                        known_gem_names = set() # Ensure it's an empty set
                except Exception as e_load_gems:
                    logger.error(f"Error loading known gem names: {e_load_gems}")
                    known_gem_names = set() # Ensure it's an empty set on error
            else:
                logger.warning("Gems collection not available, cannot load known gem names.")
                known_gem_names = set()
            # --- End Load Known Gem Names ---

        else:
             logger.warning(f"Build ChromaDB path '{build_db_path}' not found. Passive node, passive info, and gem suggestions based on build goal will not work.")
             build_chroma_client = None # Ensure build client is None if path doesn't exist
             skill_tree_collection = None
             passives_collection = None
             gems_collection = None
    except Exception as e:
        logger.error(f"Error connecting to ChromaDB at {build_db_path}: {e}")
        build_chroma_client = None
        skill_tree_collection = None
        passives_collection = None
        gems_collection = None

    # Initialize Sentence Transformer
    try:
        embedder = SentenceTransformer('all-MiniLM-L6-v2')
        logger.info("Sentence Transformer model loaded.")
    except Exception as e:
        logger.error(f"Failed to load Sentence Transformer model: {e}")
        embedder = None # Ensure embedder is None if loading fails

    # Initialize OpenAI client for local LLM
    try:
        openai_client = openai.OpenAI(
            base_url="http://127.0.0.1:1234/v1",
            api_key="not-needed"
        )
        logger.info("OpenAI client initialized for local LLM.")
    except Exception as e:
        logger.error(f"Failed to initialize OpenAI client: {e}")
        openai_client = None


    # Load full skill tree data
    try:
        nodes_path = Path("data/nodes_data.json")
        connections_path = Path("data/connections_data.json")
        if nodes_path.exists() and connections_path.exists():
            with open(nodes_path, 'r', encoding='utf-8') as f:
                all_nodes = json.load(f)
            with open(connections_path, 'r', encoding='utf-8') as f:
                all_connections = json.load(f)
            logger.info(f"Successfully loaded full skill tree data: {len(all_nodes)} nodes, {len(all_connections)} connection entries.")
        else:
            logger.warning("nodes_data.json or connections_data.json not found. Run main.py to generate them. Adjacent node filtering will not work.")
            all_nodes = None
            all_connections = None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding skill tree JSON data: {e}")
        all_nodes = None
        all_connections = None
    except Exception as e:
        logger.error(f"Error loading skill tree data: {e}")
        all_nodes = None
        all_connections = None

except Exception as e:
    logger.error(f"Error during initialization: {e}")
    # Ensure resources are None if initialization fails
    item_chroma_client = None
    build_chroma_client = None
    skill_tree_collection = None
    passives_collection = None # Added
    gems_collection = None # Added
    item_collection = None
    embedder = None
    openai_client = None

# --- Keyword Extraction ---

# Predefined list of common item types/categories relevant to Path of Exile
POE2_ITEM_KEYWORDS = {
    # Weapons
    "sword", "axe", "mace", "bow", "crossbow", "wand", "staff", "sceptre", "dagger", "claw",
    # Armour
    "helmet", "helm", "body armour", "chest", "gloves", "gauntlets", "boots", "greaves", "shield",
    # Accessories
    "ring", "amulet", "belt", "quiver",
    # General - Lower priority?
    "weapon", "armour", "gear", "item", "equipment"
}

def extract_item_keywords(user_query):
    """
    Extracts potential item keywords from the user query based on a predefined list.
    Falls back to the original user query if no specific keywords are found.
    """
    query_lower = user_query.lower()
    found_keywords = []

    # Prioritize longer/multi-word keywords first to catch phrases like "body armour"
    sorted_keywords = sorted(POE2_ITEM_KEYWORDS, key=len, reverse=True)

    temp_query = query_lower # Work on a copy for potential replacements
    processed_indices = set() # Keep track of indices covered by found keywords

    for keyword in sorted_keywords:
        start_index = 0
        while start_index < len(temp_query):
            # Use regex word boundaries for better matching
            pattern = r'\b' + re.escape(keyword) + r'\b'
            match = re.search(pattern, temp_query[start_index:])
            if match:
                match_start = start_index + match.start()
                match_end = start_index + match.end()
                # Check if this match overlaps with an already processed region
                is_overlapping = False
                for proc_start, proc_end in processed_indices:
                    if max(match_start, proc_start) < min(match_end, proc_end):
                        is_overlapping = True
                        break
                if not is_overlapping:
                    found_keywords.append(keyword)
                    processed_indices.add((match_start, match_end))
                    # Move start_index past this match to find subsequent occurrences
                    start_index = match_end
                else:
                    # Overlapping match found, skip this keyword instance and continue search
                    start_index += 1 # Move search forward slightly
            else:
                # No more matches for this keyword found from start_index
                break # Exit while loop for this keyword

    if found_keywords:
        # Use unique keywords found, maintaining order might not be critical here
        # Using set for uniqueness and then joining might be sufficient
        extracted = " ".join(sorted(list(set(found_keywords)), key=len, reverse=True)) # Sort again for consistency
        logger.info(f"Extracted item keywords: '{extracted}' from query: '{user_query}'")
        return extracted
    else:
        # Fallback: Use the original user query if no specific keywords found
        logger.info(f"No specific item keywords found in query: '{user_query}'. Using full query for item search.")
        return user_query # Fallback to user query only

# --- ChromaDB Query Functions ---
def query_passive_node_chroma(query_text, top_k=10):
    """Queries the ChromaDB skill tree collection for relevant passive nodes."""
    # Uses build_chroma_client implicitly via skill_tree_collection
    if not skill_tree_collection or not embedder:
        logger.error("ChromaDB skill_tree_collection or embedder not initialized for passive query.")
        return None, None # Return structure consistent with item query
    try:
        # Using query_texts as it's generally simpler if collection has embedding func
        results = skill_tree_collection.query(
            query_texts=[query_text],
            n_results=top_k,
            include=['metadatas', 'documents'],
            where={"type": "node"} # Ensure we only get nodes, not the build context doc
        )
        metadatas = results.get('metadatas', [[]])[0]
        documents = results.get('documents', [[]])[0]
        logger.info(f"ChromaDB passive node query returned {len(metadatas)} results for query: '{query_text}'")
        return metadatas, documents
    except Exception as e:
        logger.error(f"Error querying skill_tree_collection: {e}")
        return None, None

def query_skill_tree_chroma(query_text, top_k=5):
    """Queries the ChromaDB skill tree collection."""
    # This function might be needed if skill tree context is still used elsewhere
    # Uses build_chroma_client implicitly via skill_tree_collection
    if not skill_tree_collection or not embedder:
        logger.error("ChromaDB skill_tree_collection or embedder not initialized.")
        return None
    try:
        query_embedding = embedder.encode([query_text], convert_to_tensor=False).tolist()[0]
        results = skill_tree_collection.query(query_embeddings=[query_embedding], n_results=top_k, include=['metadatas', 'documents'])
        return results
    except Exception as e:
        logger.error(f"Error querying skill_tree_collection: {e}")
        return None

def query_item_chroma(query_text, top_k=3):
    """Queries the ChromaDB item collection."""
    # Uses item_chroma_client implicitly via item_collection
    if not item_collection or not embedder:
        logger.error("ChromaDB item_collection or embedder not initialized.")
        return None, None # Return empty lists or similar structure
    try:
        # Note: ChromaDB query() can often handle text directly with default embedding function
        # If using SentenceTransformer explicitly:
        # query_embedding = embedder.encode([query_text], convert_to_tensor=False).tolist()[0]
        # results = item_collection.query(query_embeddings=[query_embedding], n_results=top_k, include=['metadatas', 'documents'])

        # Simpler approach if collection was created with the embedding function:
        results = item_collection.query(query_texts=[query_text], n_results=top_k, include=['metadatas', 'documents'])

        metadatas = results.get('metadatas', [[]])[0]
        documents = results.get('documents', [[]])[0]
        logger.info(f"ChromaDB item query returned {len(metadatas)} results for query: '{query_text}'")
        return metadatas, documents
    except Exception as e:
        logger.error(f"Error querying item_collection: {e}")
        return None, None

def query_passives_chroma(query_text, top_k=3):
    """Queries the ChromaDB passives collection."""
    if not passives_collection or not embedder:
        logger.error("ChromaDB passives_collection or embedder not initialized.")
        return None, None
    try:
        results = passives_collection.query(
            query_texts=[query_text],
            n_results=top_k,
            include=['metadatas', 'documents']
        )
        metadatas = results.get('metadatas', [[]])[0]
        documents = results.get('documents', [[]])[0]
        logger.info(f"ChromaDB passives query returned {len(metadatas)} results for query: '{query_text}'")
        return metadatas, documents
    except Exception as e:
        logger.error(f"Error querying passives_collection: {e}")
        return None, None

def query_gems_chroma(query_text, top_k=5, gem_type_filter=None, level_filter=None):
    """
    Queries the ChromaDB gems collection.
    Optionally filters by gem type ('Skill' or 'Support') and level.
    """
    if not gems_collection or not embedder:
        logger.error("ChromaDB gems_collection or embedder not initialized.")
        return None, None
    try:
        query_params = {
            "query_texts": [query_text],
            "n_results": top_k,
            "include": ['metadatas', 'documents']
        }
        # Build the where clause dynamically
        where_clause = {}
        if gem_type_filter and gem_type_filter in ["Skill", "Support"]:
            where_clause["type"] = gem_type_filter
        if level_filter is not None:
            try:
                where_clause["level"] = int(level_filter) # Ensure level is integer for query
            except (ValueError, TypeError):
                logger.warning(f"Invalid level filter value: {level_filter}. Ignoring level filter.")

        if where_clause:
            # Handle multiple conditions if both type and level filters are present
            if len(where_clause) > 1:
                 query_params["where"] = {"$and": [{k: v} for k, v in where_clause.items()]}
                 logger.info(f"Applying combined filters: {where_clause}")
            else:
                 query_params["where"] = where_clause
                 logger.info(f"Applying filter: {where_clause}")
        else:
            logger.info("No filters applied to gem query.")

        results = gems_collection.query(**query_params)
        metadatas = results.get('metadatas', [[]])[0]
        documents = results.get('documents', [[]])[0]
        logger.info(f"ChromaDB gems query returned {len(metadatas)} results for query: '{query_text}'")
        return metadatas, documents
    except Exception as e:
        logger.error(f"Error querying gems_collection: {e}")
        return None, None

# --- Flask App ---
app = Flask(__name__)
CORS(app) # Enable CORS for all routes
# --- Endpoints ---

# --- Static File Serving ---

@app.route('/')
def serve_index():
    """Serves the main interactive skill tree HTML file."""
    logger.info(f"Serving index file: {DATA_DIR / 'poe2_skill_tree_interactive.html'}")
    return send_from_directory(DATA_DIR, 'poe2_skill_tree_interactive.html')

@app.route('/static/<path:filename>')
def serve_static(filename):
    """Serves static files (like skill_tree.js)."""
    logger.info(f"Serving static file: static/{filename}")
    # Ensure the static directory exists relative to the script
    static_dir = Path(__file__).parent / 'static'
    return send_from_directory(static_dir, filename)
@app.route('/clear_context', methods=['POST'])
def clear_context():
    """Clear persistent context for a given build/session."""
    try:
        build_id = request.args.get('build_id', 'default_build')
        from modules.context_manager import _get_context_path, load_context, save_context
        context_path = _get_context_path(build_id)

        if os.path.exists(context_path):
            os.remove(context_path)
            logger.info(f"Deleted context file for build_id '{build_id}'.")
        else:
            logger.info(f"No context file found for build_id '{build_id}'. Nothing to clear.")

        # After deletion or if file didn't exist, create a fresh empty context file
        empty_context = load_context(build_id)  # This returns default empty context if file missing
        save_context(build_id, empty_context)
        logger.info(f"Created fresh empty context file for build_id '{build_id}'.")

        return jsonify({'status': 'success', 'message': f"Context for '{build_id}' cleared and reset."})
    except Exception as e:
        logger.error(f"Error clearing context: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


# --- API Endpoints ---
@app.route('/save_nodes', methods=['POST'])
def save_nodes():
    """Saves the selected nodes received from the frontend."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data received'}), 400
        selected_nodes = data.get('selectedNodes', [])
        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)
    except Exception as e:
        print(f"Error in save_nodes: {e}")
        return jsonify({'status': 'error', 'message': 'An unexpected error occurred.'}), 500
@app.route('/upload_gear_image', methods=['POST'])
def upload_gear_image():
    """
    Handles image uploads for PoE2 gear suggestions.
    Saves uploaded images to data/uploads/ and returns a JSON response.
    """
    try:
        # Ensure upload directory exists
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)

        # Check if the request contains a file part
        if 'file' not in request.files:
            return jsonify({'status': 'error', 'message': 'No file part in the request'}), 400

        file = request.files['file']

        # If user does not select a file, browser may submit an empty part
        if file.filename == '':
            return jsonify({'status': 'error', 'message': 'No file selected'}), 400

        # Secure the filename and save the file
        filename = secure_filename(file.filename)
        save_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(save_path)

        return jsonify({'status': 'success', 'filename': filename}), 200

    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500
        with open('data/selected_nodes.json', 'w') as f:
            json.dump(selected_nodes, f, indent=4) # Added indent for readability
        logger.info(f'{len(selected_nodes)} nodes saved to data/selected_nodes.json')
        return jsonify({'message': f'{len(selected_nodes)} nodes saved successfully.'})
    except Exception as e:
        logger.error(f"Error in /save_nodes: {e}")
        return jsonify({'error': 'Failed to save nodes'}), 500


@app.route('/analyze_gear/<filename>', methods=['GET'])
def analyze_gear(filename):
    """
    Analyzes a gear image using process_gear_image and returns the extracted data as JSON.
    """
    try:
        # Build the full path to the uploaded image
        image_path = os.path.join(UPLOAD_FOLDER, filename)
        if not os.path.exists(image_path):
            return jsonify({'status': 'error', 'message': 'File not found'}), 404

        # Call the gear processor
        try:
            result = process_gear_image(image_path)
            if not isinstance(result, dict):
                return jsonify({'status': 'error', 'message': 'Processing did not return a dictionary'}), 500
            return jsonify(result)
        except Exception as e:
            return jsonify({'status': 'error', 'message': f'Error processing image: {str(e)}'}), 500

    except Exception as e:
        return jsonify({'status': 'error', 'message': f'Unexpected server error: {str(e)}'}), 500


@app.route('/load_nodes', methods=['GET'])
def load_nodes():
    """Loads and returns the selected nodes from the JSON file."""
    nodes_file_path = Path("data/selected_nodes.json")
    try:
        if nodes_file_path.exists():
            with open(nodes_file_path, 'r', encoding='utf-8') as f:
                try:
                    data = json.load(f)
                    # Basic validation: Ensure it's a list (or return empty list)
                    if not isinstance(data, list):
                        logger.warning(f"Content of {nodes_file_path} is not a list. Returning empty list.")
                        return jsonify([])
                    logger.info(f"Successfully loaded {len(data)} nodes from {nodes_file_path}")
                    return jsonify(data)
                except json.JSONDecodeError as e:
                    logger.warning(f"Error decoding JSON from {nodes_file_path}: {e}. Returning empty list.")
                    return jsonify([])
        else:
            logger.info(f"{nodes_file_path} not found. Returning empty list.")
            return jsonify([])
    except Exception as e:
        logger.error(f"Error reading {nodes_file_path}: {e}")
        return jsonify({'error': f'Failed to load nodes from {nodes_file_path}'}), 500


@app.route('/get_suggestion', methods=['GET'])
def get_suggestion():
    """
    Receives a user query, fetches context from ChromaDB based on the query
    and selected nodes, queries the local LLM, and returns a suggestion.
    """
    import json
    # Check for necessary components including the loaded skill tree data
    if not openai_client or not all_nodes or not all_connections:
        missing = []
        if not openai_client: missing.append("OpenAI client")
        if not all_nodes: missing.append("Skill tree nodes data")
        if not all_connections: missing.append("Skill tree connections data")
        logger.error(f"Server not fully initialized. Missing: {', '.join(missing)}. Run main.py if data files are missing.")
        return jsonify({'error': f'Server initialization incomplete. Missing: {", ".join(missing)}. Cannot process suggestion request.'}), 503 # Service Unavailable

    try:
        # Extract parameters from URL query string
        user_query = request.args.get('user_query')
        build_goal = request.args.get('build_goal', 'General advice')  # Extract build goal with default
        selected_stats_str = request.args.get('selected_stats', '')  # Get selected stats string (like in ask_general)
        build_id = request.args.get('build_id', 'default_build')  # Extract build_id with default

        # Load persistent context dict
        try:
            from modules.context_manager import load_context, save_context
            persistent_context = load_context(build_id)
        except Exception as e:
            logger.warning(f"Failed to load persistent context for build_id '{build_id}': {e}")
            persistent_context = {"history": []}

        # Parse incoming history from request
        if not user_query:
            return jsonify({'error': 'Missing "user_query" in request data'}), 400
        # Extract and parse conversation history
        history_json = request.args.get('history', '[]')
        incoming_history = []
        try:
            incoming_history = json.loads(history_json)
            if not isinstance(incoming_history, list):
                logger.warning(f"Parsed incoming history is not a list ({type(incoming_history)}). Defaulting to empty list.")
                incoming_history = []
        except json.JSONDecodeError:
            logger.warning(f"Failed to decode incoming history JSON: {history_json}. Defaulting to empty list.")
            incoming_history = []
        except Exception as e:
            logger.error(f"Unexpected error processing incoming history: {e}. Defaulting to empty list.")
            incoming_history = []

        # Merge persistent context history with incoming history
        persistent_history = persistent_context.get("history", [])
        # Simple merge: concatenate and deduplicate by (role, content) tuple
        combined_history = persistent_history + incoming_history
        seen = set()
        merged_history = []
        for msg in combined_history:
            key = (msg.get("role"), msg.get("content"))
            if key not in seen:
                seen.add(key)
                merged_history.append(msg)

        # Update persistent context with merged history
        persistent_context["history"] = merged_history
        try:
            save_context(build_id, persistent_context)
        except Exception as e:
            logger.warning(f"Failed to save updated persistent context for build_id '{build_id}': {e}")

        # Use merged_history as the conversation history
        history = merged_history


        # Load currently selected nodes (optional context)
        selected_nodes_data = [] # Renamed to avoid confusion with the variable name in process_data
        selected_nodes_str = "None"
        allocated_node_ids_set = set() # Initialize for use in adjacent node logic etc.
        allocated_path_ids = [] # Initialize for use in adjacent node logic etc.
        try:
            with open('data/selected_nodes.json', 'r') as f:
                loaded_nodes = json.load(f)
                # Ensure loaded_nodes is a list of dictionaries
                if isinstance(loaded_nodes, list):
                    selected_nodes_data = [node for node in loaded_nodes if isinstance(node, dict) and 'id' in node] # Filter for valid node dicts
                    allocated_node_ids_set = {node['id'] for node in selected_nodes_data}
                    allocated_path_ids = [node['id'] for node in selected_nodes_data] # Keep path IDs if needed elsewhere
                    # Keep the string representation for the LLM prompt as before
                    selected_nodes_str = ", ".join([node.get('label', 'Unknown Node') for node in selected_nodes_data]) if selected_nodes_data else "None"
                else:
                    logger.warning("selected_nodes.json did not contain a list. Proceeding without selected nodes context.")
                    selected_nodes_data = [] # Ensure it's an empty list
        except FileNotFoundError: # Align with try
            logger.warning("data/selected_nodes.json not found. Proceeding without selected nodes context.")
        except json.JSONDecodeError: # Align with try
             logger.warning("Error decoding data/selected_nodes.json. Proceeding without selected nodes context.")
        except Exception as e: # Align with try
            logger.error(f"Error loading selected_nodes.json: {e}")
            # Decide if this should be a hard error or just a warning
            # return jsonify({'error': 'Failed to load selected node data'}), 500

        # Calculate character level based on selected nodes
        character_level = len(selected_nodes_data)
        logger.info(f"Calculated character level: {character_level}")

        # --- Improved Logic: Query relevant nodes, find reachable, prioritize ---
        logger.info(f"Starting passive node suggestion logic for {len(allocated_node_ids_set)} allocated nodes.")
        initial_top_k = 30 # Increased top_k

        # 1. Query ChromaDB - Strategy 1: Build Goal Only
        logger.info(f"Querying passive nodes with build_goal: '{build_goal}' (top_k={initial_top_k})")
        relevant_metadatas, relevant_documents = query_passive_node_chroma(build_goal, top_k=initial_top_k)

        # 1b. Query ChromaDB - Strategy 2: Fallback to Combined Query
        if not relevant_metadatas:
            logger.info(f"Build goal query yielded no results. Falling back to combined query.")
            passive_query_text = f"{build_goal} {user_query}"
            logger.info(f"Querying passive nodes with combined text: '{passive_query_text}' (top_k={initial_top_k})")
            relevant_metadatas, relevant_documents = query_passive_node_chroma(passive_query_text, top_k=initial_top_k)

        # Process results from the successful query
        primary_relevant_nodes = []
        if relevant_metadatas:
            logger.info(f"Initial query yielded {len(relevant_metadatas)} results.")
            for meta, doc in zip(relevant_metadatas, relevant_documents):
                node_id = meta.get('node_id')
                if node_id and node_id in all_nodes: # Ensure node exists in our main data
                    primary_relevant_nodes.append({
                        "id": node_id,
                        "name": all_nodes[node_id].get("name", "Unknown"),
                        "stats": all_nodes[node_id].get("stats", "N/A"),
                        "document": doc # Keep the document for context if needed
                    })
            # Filter out already allocated nodes from primary relevant nodes
            primary_relevant_nodes = [
                node for node in primary_relevant_nodes
                if node['id'] not in allocated_node_ids_set
            ]
            logger.info(f"Processed {len(primary_relevant_nodes)} potentially relevant nodes from initial query.")
        else:
            logger.warning("Both build goal and combined queries yielded no results.")

        # 2. Get adjacent, unallocated nodes
        adjacent_unallocated_nodes_list = []
        adjacent_unallocated_ids_set = set()
        if all_nodes and all_connections:
            # Perform lookahead pathfinding to find reachable nodes within depth 3
            lookahead_future_nodes = lookahead_pathfinding(
                allocated_node_ids=allocated_path_ids,
                all_nodes=all_nodes,
                connections=all_connections,
                max_depth=3,
                allocated_set=allocated_node_ids_set
            )

            # Convert adjacent nodes list to dict for easy merging
            adjacent_nodes_dict = {node['id']: node for node in adjacent_unallocated_nodes_list}

            # Add lookahead nodes if not already adjacent and not allocated
            for node_id, path in lookahead_future_nodes.items():
                if node_id not in adjacent_nodes_dict and node_id not in allocated_node_ids_set:
                    node_data = all_nodes.get(node_id, {})
                    adjacent_nodes_dict[node_id] = {
                        "id": node_id,
                        "name": node_data.get("name", "Unknown"),
                        "stats": node_data.get("stats", "N/A"),
                        "description": node_data.get("description", ""),
                        "path": path
                    }

            # Replace adjacent_unallocated_nodes_list with combined adjacent + lookahead nodes
            adjacent_unallocated_nodes_list = list(adjacent_nodes_dict.values())
            adjacent_unallocated_nodes_list = get_adjacent_unallocated_nodes(
                allocated_path_ids=allocated_path_ids,
                all_nodes=all_nodes,
                connections=all_connections
            )
            adjacent_unallocated_ids_set = {node['id'] for node in adjacent_unallocated_nodes_list}
            logger.info(f"Found {len(adjacent_unallocated_ids_set)} adjacent, unallocated node IDs.")
        else:
            logger.warning("Skill tree data not loaded, cannot determine adjacent nodes.")

        # --- Specific Filter: Exclude node 2461 if last node is 44605 ---
        if allocated_path_ids and allocated_path_ids[-1] == "44605":
            original_count = len(adjacent_unallocated_nodes_list)
            adjacent_unallocated_nodes_list = [
                node for node in adjacent_unallocated_nodes_list if node.get("id") != "2461"
            ]
            if len(adjacent_unallocated_nodes_list) < original_count:
                logger.info("Filtered out node 2461 as per specific rule for node 44605.")
            # Update the set as well, in case it's used later (though current logic doesn't seem to)
            adjacent_unallocated_ids_set = {node['id'] for node in adjacent_unallocated_nodes_list}
        # --- End Specific Filter ---

        # 3. Targeted Adjacent Query: Check if *reachable* nodes match the build goal
        relevant_adjacent_nodes = []
        relevant_adjacent_ids_set = set()
        if adjacent_unallocated_ids_set and skill_tree_collection and embedder:
            logger.info(f"Performing targeted query for {len(adjacent_unallocated_ids_set)} adjacent nodes using build_goal: '{build_goal}'")
            try:
                # Query ChromaDB specifically for the adjacent node IDs using the build goal
                adj_results = skill_tree_collection.query(
                    query_texts=[build_goal],
                    n_results=len(adjacent_unallocated_ids_set), # Limit results to max possible adjacent
                    include=['metadatas', 'documents'],
                    where={
                        "$and": [
                            {"type": "node"}, # Ensure it's a node
                            {"node_id": {"$in": list(adjacent_unallocated_ids_set)}} # Filter by adjacent IDs
                        ]
                    }
                )
                adj_metadatas = adj_results.get('metadatas', [[]])[0]
                adj_documents = adj_results.get('documents', [[]])[0]
                logger.info(f"Targeted adjacent query returned {len(adj_metadatas)} results.")

                if adj_metadatas:
                    for meta, doc in zip(adj_metadatas, adj_documents):
                        node_id = meta.get('node_id')
                        # Double-check if the result is indeed one of the adjacent nodes
                        if node_id and node_id in adjacent_unallocated_ids_set and node_id in all_nodes:
                            relevant_adjacent_nodes.append({
                                "id": node_id,
                                "name": all_nodes[node_id].get("name", "Unknown"),
                                "stats": all_nodes[node_id].get("stats", "N/A"),
                                "document": doc
                            })
                            relevant_adjacent_ids_set.add(node_id)
                    logger.info(f"Processed {len(relevant_adjacent_nodes)} relevant nodes from targeted adjacent query.")

            except Exception as e:
                logger.error(f"Error during targeted adjacent query: {e}")
        elif not adjacent_unallocated_ids_set:
             logger.info("No adjacent nodes to perform targeted query on.")
        else:
             logger.warning("Skill tree collection or embedder not available for targeted adjacent query.")
        # --- Fallback Passive Retrieval if adjacent nodes are very few ---
        formatted_fallback_passives = []
        try:
            if len(adjacent_unallocated_nodes_list) <= 2:
                logger.info(f"Adjacent unallocated nodes are very few ({len(adjacent_unallocated_nodes_list)}). Triggering fallback passive retrieval.")
                fallback_query_text = f"{build_goal} {user_query}"
                fallback_top_k = 5
                fallback_metadatas, fallback_documents = query_passives_chroma(fallback_query_text, top_k=fallback_top_k)
                if fallback_metadatas:
                    for meta, doc in zip(fallback_metadatas, fallback_documents):
                        passive_name = meta.get('name', 'Passive Skill')
                        formatted_fallback_passives.append(f"- {passive_name}: {doc}")
                    logger.info(f"Retrieved {len(formatted_fallback_passives)} fallback passive skills.")
                else:
                    logger.info("Fallback passive retrieval returned no results.")
            else:
                logger.info(f"Adjacent unallocated nodes sufficient ({len(adjacent_unallocated_nodes_list)}), skipping fallback passive retrieval.")
        except Exception as e:
            logger.error(f"Error during fallback passive retrieval: {e}")



        # 4. Find the intersection: Nodes from initial query that are ALSO reachable
        reachable_primary_relevant_nodes = []
        if primary_relevant_nodes and adjacent_unallocated_ids_set:
            for node in primary_relevant_nodes:
                # Check if node from the broad query is reachable AND wasn't already found by the targeted query
                if node['id'] in adjacent_unallocated_ids_set and node['id'] not in relevant_adjacent_ids_set:
                    reachable_primary_relevant_nodes.append(node)
        logger.info(f"Found {len(reachable_primary_relevant_nodes)} nodes from initial query that are reachable (and not already identified by targeted query).")

        # 5. Build context_str for the LLM, prioritizing actual adjacent nodes
        adjacent_limit = 15
        formatted_adjacent_nodes = []
        adjacent_ids_in_context = set()  # Keep track of IDs added from adjacent list

        # Filter adjacent_unallocated_nodes_list to ONLY passive nodes
        filtered_adjacent_nodes = [
            node for node in adjacent_unallocated_nodes_list
            if node.get("type") == "node" and node.get("id") not in allocated_node_ids_set
        ]

        # Prepare passive context data for saving
        passive_context_data = {
            "build_goal": build_goal,
            "adjacent_nodes": []
        }

        logger.info(f"Formatting up to {adjacent_limit} adjacent PASSIVE nodes for context.")
        for node in filtered_adjacent_nodes[:adjacent_limit]:
            node_id = node.get('id', 'N/A')
            node_name = node.get('name', 'Unknown')
            node_stats = node.get('stats', 'N/A')
            node_desc = node.get('description', '')

            formatted_adjacent_nodes.append(
                f"- Node ID: {node_id}\n  Node Name: {node_name}\n  Node Effect: {node_stats}"
            )
            adjacent_ids_in_context.add(node_id)

            passive_context_data["adjacent_nodes"].append({
                "id": node_id,
                "name": node_name,
                "stats": node_stats,
                "description": node_desc
            })

        logger.info(f"Added {len(formatted_adjacent_nodes)} adjacent PASSIVE nodes to context.")

        # Save passive_context_data into default_build.json
        import os, json
        build_context_path = "data/build_contexts/default_build.json"
        os.makedirs(os.path.dirname(build_context_path), exist_ok=True)
        try:
            if os.path.exists(build_context_path):
                with open(build_context_path, "r", encoding="utf-8") as f:
                    build_context = json.load(f)
            else:
                build_context = {}

            build_context["passive_context"] = passive_context_data

            # Initialize history if missing
            if "history" not in build_context or not isinstance(build_context["history"], list):
                build_context["history"] = []

            with open(build_context_path, "w", encoding="utf-8") as f:
                json.dump(build_context, f, indent=2, ensure_ascii=False)
            logger.info("Saved passive_context to default_build.json.")
        except Exception as e:
            logger.error(f"Failed to save passive_context to default_build.json: {e}")

        # Skip adding other relevant nodes from ChromaDB to context (per new instructions)
        formatted_other_nodes = []

        # Old context_str and passive_context_note logic removed.
        # New context variables will be prepared below after all individual context parts are fetched.

        # --- End of Passive Node Context Generation ---

        # --- New Logic: LLM-driven item retrieval ---
        item_context_str = "No relevant item examples found."
        try:
            item_types = extract_item_keywords(user_query)
            primary_skill = {"name": user_query, "tags": ""}
            key_stats = []  # Placeholder, can be extended later
            relevant_items = query_relevant_items(primary_skill, key_stats, item_types)

            # Define projectile-related keywords
            projectile_keywords = ["projectile", "additional projectile", "chain", "fires extra", "split", "fork"]

            # Check if user query is projectile-related
            user_query_lower = user_query.lower()
            is_projectile_query = any(kw in user_query_lower for kw in projectile_keywords)

            filtered_items = []
            if is_projectile_query:
                # Strict filtering: only items whose implicit_stats contain projectile keywords
                for item in relevant_items:
                    implicit_stats = item.get('implicit_stats', [])
                    implicit_text = " ".join(implicit_stats).lower() if isinstance(implicit_stats, list) else str(implicit_stats).lower()
                    if any(kw in implicit_text for kw in projectile_keywords):
                        filtered_items.append(item)
                relevant_items = filtered_items
            # else: leave relevant_items unchanged (no projectile filtering)

            if relevant_items:
                formatted_items = []
                for item in relevant_items:
                    name = item.get('name', 'Unknown Item')
                    base_type = item.get('base_type', '')
                    description = item.get('description', '')
                    implicit_stats = item.get('implicit_stats', [])
                    explicit_stats = item.get('explicit_stats', [])

                    # For labeling, check if projectile-enhancing (implicit only if projectile query)
                    implicit_text = " ".join(implicit_stats).lower() if isinstance(implicit_stats, list) else str(implicit_stats).lower()
                    explicit_text = " ".join(explicit_stats).lower() if isinstance(explicit_stats, list) else str(explicit_stats).lower()

                    if is_projectile_query:
                        is_projectile = any(kw in implicit_text for kw in projectile_keywords)
                    else:
                        is_projectile = any(kw in implicit_text or kw in explicit_text for kw in projectile_keywords)

                    label = "**[Projectile-Enhancing]** " if is_projectile else ""

                    # Convert stats to string if they are lists
                    if isinstance(implicit_stats, list):
                        implicit_stats_str = "; ".join(implicit_stats)
                    else:
                        implicit_stats_str = str(implicit_stats)

                    if isinstance(explicit_stats, list):
                        explicit_stats_str = "; ".join(explicit_stats)
                    else:
                        explicit_stats_str = str(explicit_stats)

                    formatted_items.append(
                        f"- {label}{name} ({base_type})\n"
                        f"  Description: {description}\n"
                        f"  Implicit Stats: {implicit_stats_str}\n"
                        f"  Explicit Stats: {explicit_stats_str}"
                    )
                if formatted_items:
                    item_context_str = "Relevant item examples based on your query:\n" + "\n".join(formatted_items)
        except Exception as e:
            logger.warning(f"LLM-driven item retrieval failed: {e}")
        # --- End of LLM-driven Item Retrieval ---

        # --- New Logic: Query ChromaDB for relevant passive skill descriptions ---
        passive_info_context_str = "No relevant passive skill information found." # Default message
        if passives_collection: # Check if passives collection was loaded successfully
            passive_query_text = f"{build_goal} {user_query}"
            logger.info(f"Querying passives collection with text: '{passive_query_text}'")
            passive_metadatas, passive_documents = query_passives_chroma(passive_query_text, top_k=3)

            if passive_metadatas:
                formatted_passives = []
                for meta, doc in zip(passive_metadatas, passive_documents):
                    # Assuming metadata might contain 'name' or similar, fallback to doc
                    passive_name = meta.get('name', 'Passive Info')
                    formatted_passives.append(f"- {passive_name}: {doc}")
                if formatted_passives:
                    passive_info_context_str = "Relevant passive skill info based on your query:\n" + "\n".join(formatted_passives)
            else:
                 logger.warning("Passives query returned no results.")
        else:
            logger.warning("Passives collection not available. Skipping passive info context query.")
        # --- End of Passive Info Query Logic ---

        # --- New Logic: Query ChromaDB for relevant gems ---
        gem_context_str = "No relevant gem examples found." # Default message
        if gems_collection: # Check if gems collection was loaded successfully
            gem_query_text = f"{build_goal} {user_query}"
            logger.info(f"Querying gems collection with text: '{gem_query_text}'")
            gem_metadatas, gem_documents = query_gems_chroma(gem_query_text, top_k=3)

            if gem_metadatas:
                formatted_gems = []
                for meta, doc in zip(gem_metadatas, gem_documents):
                    # Extract relevant info - adjust based on actual metadata keys
                    name = meta.get('name', 'Unknown Gem')
                    tags = meta.get('tags', '')
                    formatted_gems.append(f"- {name} ({tags}): {doc}")
                if formatted_gems:
                    gem_context_str = "Relevant gem examples based on your query:\n" + "\n".join(formatted_gems)
            else:
                 logger.warning("Gems query returned no results.")
        else:
            logger.warning("Gems collection not available. Skipping gem context query.")
        # --- End of Gem Query Logic ---
        # Prepare context strings for the new LLM prompt structure
        adjacent_options_formatted_list = "\n".join(formatted_adjacent_nodes) if formatted_adjacent_nodes else "No adjacent unallocated nodes found."
        if not allocated_node_ids_set and not formatted_adjacent_nodes: # Handle case where no nodes are allocated yet
            adjacent_options_formatted_list = "No nodes allocated yet."

        other_context_parts = []
        if formatted_other_nodes:
             other_context_parts.append("Other Relevant Nodes (May not be adjacent):\n" + "\n".join(formatted_other_nodes))

        # Add fallback passive skills if any were retrieved
        if formatted_fallback_passives:
            other_context_parts.append("**Fallback Passive Skills (Important):**\n" + "\n".join(formatted_fallback_passives))
        # Use the finalized context strings from the query logic above
        if item_context_str and item_context_str != "No relevant item examples found.":
             other_context_parts.append(item_context_str)
        if passive_info_context_str and passive_info_context_str != "No relevant passive skill information found.":
             other_context_parts.append(passive_info_context_str)
        if gem_context_str and gem_context_str != "No relevant gem examples found.":
             other_context_parts.append(gem_context_str)
        other_context_str = "\n\n".join(other_context_parts) if other_context_parts else "No additional context available."

        # --- Calculate Accumulated Stats ---
        accumulated_stats_str = "Could not calculate accumulated stats."
        # Pass the full selected_nodes_data (list of dicts) to the calculation function
        if selected_nodes_data and all_nodes:
            try:
                accumulated_stats_str = calculate_accumulated_stats(selected_nodes_data, all_nodes)
                logger.info("Successfully calculated accumulated stats.")
            except Exception as e_stats:
                logger.error(f"Error calculating accumulated stats: {e_stats}")
        elif not allocated_node_ids_set:
             accumulated_stats_str = "No nodes selected yet."
        # --- End Accumulated Stats Calculation ---

        # Call the streaming LLM function
        logger.info("Calling streaming LLM function.")
        passive_context_note = "Context lists adjacent nodes first, then other relevant info." # Define the missing variable
        # build_id already extracted above
        stream = get_llm_suggestion_stream(
            openai_client=openai_client,
            build_id=build_id,
            build_goal=build_goal,
            character_level=character_level,
            selected_nodes_list=selected_nodes_data, # Pass the list of node objects
            accumulated_stats_str=accumulated_stats_str,
            adjacent_options_formatted_list=adjacent_options_formatted_list,
            other_context_str=other_context_str,
            item_context_str=item_context_str,
            passive_info_context_str=passive_info_context_str,
            gem_context_str=gem_context_str,
            selected_stats_str=selected_stats_str, # Pass the selected stats string
            user_query=user_query,
            passive_context_note=passive_context_note,
            history=history # Pass history if needed/available
        )

        # Return a streaming response using Server-Sent Events (SSE)
        return Response(stream_with_context(stream), mimetype='text/event-stream')

    except Exception as e:
        logger.error(f"Error in /get_suggestion: {e}")
        # Consider more specific error handling based on exception type
        return jsonify({'error': 'An unexpected error occurred processing the suggestion request.'}), 500



@app.route('/ask_general', methods=['GET'])
def ask_general():
    """
    Receives a user query and build goal, fetches general context from ChromaDB
    (items, passives, gems), queries the local LLM, and returns a suggestion
    WITHOUT using the current selected nodes from the skill tree.
    """
    # Check for necessary components (excluding node data)
    if not openai_client:
        logger.error("Server not fully initialized. Missing: OpenAI client.")
        # Use a generator to yield the error as SSE
        def error_stream():
            yield f"data: {json.dumps({'error': 'Server initialization incomplete. Missing OpenAI client.'})}\n\n"
        return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=503)

    try:
        # Extract parameters from URL query string
        user_query = request.args.get('user_query')
        build_goal = request.args.get('build_goal', 'General advice') # Extract build goal with default
        history_json = request.args.get('history', '[]') # Get history JSON string
        selected_stats_str = request.args.get('selected_stats', '') # Get selected stats string

        if not user_query:
            # Use a generator to yield the error as SSE
            def error_stream():
                yield f"data: {json.dumps({'error': 'Missing user_query in request data'})}\n\n"
            return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=400)

        # Parse history JSON
        try:
            history = json.loads(history_json)
            if not isinstance(history, list):
                 logger.warning(f"Received history is not a list: {history}. Resetting to empty list.")
                 history = []
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON received for history: {history_json}. Resetting to empty list.")
            history = []

        logger.info(f"Received general query: '{user_query}', Build Goal: '{build_goal}', History length: {len(history)}")
        # 1. Parse Level from Query
        parsed_level = None
        level_match = re.search(r"(?:level|lvl)\s+(\d+)", user_query.lower())
        if level_match:
            try:
                parsed_level = int(level_match.group(1))
                logger.info(f"Parsed level from query: {parsed_level}")
            except ValueError:
                logger.warning(f"Could not parse level from match: {level_match.group(1)}")
        # --- End Level Parsing ---


        # --- Dynamic Context Limits ---
        query_lower = user_query.lower()
        ITEM_KEYWORDS = {"weapon", "item", "armor", "gear", "sword", "axe", "mace", "bow", "wand", "shield", "helmet", "chest", "gloves", "boots", "ring", "amulet", "belt"}
        GEM_KEYWORDS = {"gem", "skill", "aura", "curse", "buff", "support", "spell", "attack", "minion"}
        PASSIVE_KEYWORDS = {"passive", "node", "tree", "skill tree", "mastery", "notable"}

        BASE_ITEMS = 20
        BASE_PASSIVES = 20
        BASE_GEMS = 20
        BOOSTED_LIMIT = 25
        GEM_BOOSTED_LIMIT = 40 # Gems might need a slightly higher boost

        n_items = BASE_ITEMS
        n_passives = BASE_PASSIVES
        n_gems = BASE_GEMS

        # Check for keywords (prioritize items > gems > passives if overlap)
        if any(keyword in query_lower for keyword in ITEM_KEYWORDS):
            logger.info("Item keywords detected. Boosting item results.")
            n_items = BOOSTED_LIMIT
            n_gems = 0
        elif any(keyword in query_lower for keyword in GEM_KEYWORDS):
            logger.info("Gem keywords detected. Boosting gem results.")
            n_gems = GEM_BOOSTED_LIMIT
            n_items = 0
        elif any(keyword in query_lower for keyword in PASSIVE_KEYWORDS):
            logger.info("Passive keywords detected. Boosting passive results.")
            n_passives = BOOSTED_LIMIT
            n_items = 0
            n_gems = 0
        else:
            logger.info("No specific keywords detected. Using base limits for context.")

        # Enforce minimum passives retrieval count regardless of keyword detection
        MIN_PASSIVES = 20
        if n_passives < MIN_PASSIVES:
            n_passives = MIN_PASSIVES

        logger.info(f"Context limits set: Items={n_items}, Passives={n_passives}, Gems={n_gems}")
        # --- End Dynamic Context Limits ---

        # --- Query ChromaDB for relevant items --- (Same as /get_suggestion)
        # --- New Logic: LLM-driven item retrieval ---
        item_context_str = "No relevant item examples found."
        try:
            item_types = extract_item_keywords(user_query)
            primary_skill = {"name": user_query, "tags": ""}
            key_stats = []  # Placeholder, can be extended later
            relevant_items = query_relevant_items(primary_skill, key_stats, item_types)

            # --- Post-filtering to prioritize projectile-related crossbows ---
            projectile_keywords = ["projectile", "additional projectile", "chain", "fires extra", "split", "fork"]
            projectile_items = []
            other_items = []

            for item in relevant_items:
                implicit_stats = item.get('implicit_stats', [])
                explicit_stats = item.get('explicit_stats', [])

                # Convert stats to lower-case strings for matching
                implicit_text = " ".join(implicit_stats).lower() if isinstance(implicit_stats, list) else str(implicit_stats).lower()
                explicit_text = " ".join(explicit_stats).lower() if isinstance(explicit_stats, list) else str(explicit_stats).lower()

                if any(kw in implicit_text or kw in explicit_text for kw in projectile_keywords):
                    projectile_items.append(item)
                else:
                    other_items.append(item)

            # Prioritize projectile items first, then append a few others
            relevant_items = projectile_items + other_items[:3]
            if relevant_items:
                formatted_items = []
                for item in relevant_items:
                    name = item.get('name', 'Unknown Item')
                    base_type = item.get('base_type', '')
                    description = item.get('description', '')
                    implicit_stats = item.get('implicit_stats', [])
                    explicit_stats = item.get('explicit_stats', [])

                    # Check if projectile-enhancing
                    implicit_text = " ".join(implicit_stats).lower() if isinstance(implicit_stats, list) else str(implicit_stats).lower()
                    explicit_text = " ".join(explicit_stats).lower() if isinstance(explicit_stats, list) else str(explicit_stats).lower()
                    is_projectile = any(kw in implicit_text or kw in explicit_text for kw in ["projectile", "additional projectile", "chain", "fires extra", "split", "fork"])

                    # Add label if projectile-enhancing
                    label = "**[Projectile-Enhancing]** " if is_projectile else ""

                    # Convert stats to string if they are lists
                    if isinstance(implicit_stats, list):
                        implicit_stats_str = "; ".join(implicit_stats)
                    else:
                        implicit_stats_str = str(implicit_stats)

                    if isinstance(explicit_stats, list):
                        explicit_stats_str = "; ".join(explicit_stats)
                    else:
                        explicit_stats_str = str(explicit_stats)

                    formatted_items.append(
                        f"- {name} ({base_type})\n"
                        f"  Description: {description}\n"
                        f"  Implicit Stats: {implicit_stats_str}\n"
                        f"  Explicit Stats: {explicit_stats_str}"
                    )
                if formatted_items:
                    item_context_str = "Relevant item examples based on your query:\n" + "\n".join(formatted_items)
        except Exception as e:
            logger.warning(f"LLM-driven item retrieval failed: {e}")
        # --- End of LLM-driven Item Retrieval ---

        # --- Query ChromaDB for relevant passive skill descriptions --- (Same as /get_suggestion)
        passive_info_context_str = "No relevant passive skill information found."
        if passives_collection:
            passive_query_text = f"{build_goal} {user_query}"
            logger.info(f"Querying passives collection with text: '{passive_query_text}'")
            passive_metadatas, passive_documents = query_passives_chroma(passive_query_text, top_k=n_passives) # Use dynamic limit
            if passive_metadatas:
                formatted_passives = []
                for meta, doc in zip(passive_metadatas, passive_documents):
                    passive_name = meta.get('name', 'Passive Info')
                    formatted_passives.append(f"- {passive_name}: {doc}")
                if formatted_passives:
                    passive_info_context_str = "Relevant passive skill info based on your query:\n" + "\n".join(formatted_passives)
            else:
                 logger.warning("Passives query returned no results.")
        else:
            logger.warning("Passives collection not available. Skipping passive info context query.")

        # --- Query ChromaDB for relevant gems ---
        gem_context_str = "No relevant gem examples found."
        if gems_collection:
            gem_query_text = f"{build_goal} {user_query}"
            # --- Determine Gem Type and Level Filters ---
            requested_gem_type = None
            requested_level = None
            query_lower = user_query.lower()

            # Type detection
            if "support gem" in query_lower or "link with" in query_lower or "support for" in query_lower:
                requested_gem_type = "Support"
            elif "skill gem" in query_lower or "active skill" in query_lower:
                 requested_gem_type = "Skill"

            # Level detection (simple regex for "level X" or "lvl X")
            level_match = re.search(r"(?:level|lvl)\s+(\d+)", query_lower)
            if level_match:
                try:
                    requested_level = int(level_match.group(1))
                except ValueError:
                    logger.warning(f"Could not parse level from query: {level_match.group(1)}")

            # Buff/Aura keyword detection
            is_buff_query = False
            if "buff" in query_lower or "aura" in query_lower:
                is_buff_query = True
                logger.info("Detected 'buff' or 'aura' keyword in query.")
            # --- End Determine Filters ---

            # --- Find Mentioned Known Gem Names ---
            mentioned_gem_names = []
            if known_gem_names: # Check if the set was loaded
                query_lower = user_query.lower()
                for known_name in known_gem_names:
                    if known_name.lower() in query_lower:
                        mentioned_gem_names.append(known_name) # Append original capitalization
                if mentioned_gem_names:
                    logger.info(f"Found known gem names mentioned in query: {mentioned_gem_names}")
                else:
                    logger.info("No known gem names found mentioned in query.")
            else:
                logger.warning("Known gem name list is not available for checking.")
            # --- End Find Mentioned Known Gem Names ---

            # --- Perform Targeted Gem Queries ---
            targeted_gem_metadatas = []
            targeted_gem_documents = []
            if mentioned_gem_names and gems_collection:
                logger.info(f"Performing targeted queries for {len(mentioned_gem_names)} mentioned known gem names...")
                for name in mentioned_gem_names: # Iterate through known mentioned names
                    try:
                        target_where_conditions = {"name": name}
                        # Add level filter if detected earlier
                        if requested_level is not None:
                            target_where_conditions["level"] = requested_level

                        # Construct the final where clause using $and if multiple conditions exist
                        if len(target_where_conditions) > 1:
                            final_where_clause = {"$and": [{k: v} for k, v in target_where_conditions.items()]}
                        elif len(target_where_conditions) == 1:
                            final_where_clause = target_where_conditions
                        else: # Should not happen if name is always present, but safety check
                            final_where_clause = None

                        logger.info(f"Targeted query for: '{name}' with where clause: {final_where_clause}")

                        if final_where_clause: # Only query if we have a valid where clause
                            targeted_results = gems_collection.query(
                                query_texts=[name], # Query text can be the name itself for filtering
                                n_results=1,        # We only need one match for the exact name
                                where=final_where_clause,
                                include=['metadatas', 'documents'] # Correct include parameter
                            )
                        else:
                            targeted_results = None # Skip query if where clause is empty

                        # Check if results are valid and contain data
                        if (targeted_results and
                            targeted_results.get('ids') and
                            targeted_results['ids'][0] and # Check if the inner list for the first query text is not empty
                            targeted_results.get('metadatas') and
                            targeted_results['metadatas'][0] and
                            targeted_results.get('documents') and
                            targeted_results['documents'][0]):

                            # Extract the first (and likely only) result
                            t_meta = targeted_results['metadatas'][0][0]
                            t_doc = targeted_results['documents'][0][0]
                            t_id = targeted_results['ids'][0][0]

                            logger.info(f"Targeted query found match for '{name}' (ID: {t_id})")
                            targeted_gem_metadatas.append(t_meta)
                            targeted_gem_documents.append(t_doc)
                        else:
                             logger.info(f"Targeted query for '{name}' found no results matching criteria.")

                    except Exception as e_target:
                        logger.error(f"Error during targeted query for '{name}': {e_target}")

            # --- End Targeted Gem Queries ---

            # --- Perform Buff-Specific Gem Query (if keywords detected) ---
            buff_gem_metadatas = []
            buff_gem_documents = []
            if is_buff_query and gems_collection:
                buff_query_text = "buff gem aura" # Specific query text for buffs/auras
                logger.info(f"Querying gems collection specifically for buffs/auras with text: '{buff_query_text}' (Level Filter: {requested_level})")
                try:
                    # Use query_gems_chroma, applying only the level filter
                    buff_results = query_gems_chroma(
                        buff_query_text,
                        top_k=15, # Fetch a decent amount for potential buff gems
                        level_filter=requested_level
                        # No type filter here, let the query text handle relevance
                    )
                    buff_gem_metadatas = buff_results[0] if buff_results and buff_results[0] else []
                    buff_gem_documents = buff_results[1] if buff_results and buff_results[1] else []
                    logger.info(f"Buff-specific search returned {len(buff_gem_metadatas)} potential gems.")
                except Exception as e_buff:
                    logger.error(f"Error during buff-specific gem query: {e_buff}")
            # --- End Buff-Specific Gem Query ---
            # --- End Targeted Gem Queries ---
            # --- Perform Semantic Gem Query ---
            semantic_gem_metadatas = []
            semantic_gem_documents = []
            if gems_collection: # Ensure collection exists before querying
                logger.info(f"Querying gems collection semantically with text: '{gem_query_text}' (Type Filter: {requested_gem_type}, Level Filter: {requested_level})")
                try:
                    # Use a reasonable top_k for semantic search, fetch a bit more for deduplication
                    # Assuming query_gems_chroma is imported and returns (metadatas, documents)
                    semantic_results = query_gems_chroma(
                        gem_query_text,
                        top_k=n_gems, # Use dynamic limit for general semantic query
                        gem_type_filter=requested_gem_type,
                        level_filter=requested_level
                    )
                    # query_gems_chroma might return None or empty lists if no results
                    semantic_gem_metadatas = semantic_results[0] if semantic_results and semantic_results[0] else []
                    semantic_gem_documents = semantic_results[1] if semantic_results and semantic_results[1] else []
                    logger.info(f"Semantic search returned {len(semantic_gem_metadatas)} potential gems.")
                except Exception as e_semantic:
                    logger.error(f"Error during semantic gem query: {e_semantic}")
            # --- End Semantic Gem Query ---

            # --- Merge Targeted, Buff, and Semantic Results ---
            MAX_CONTEXT_GEMS = 50 # Define the maximum number of gems to include in the context
            final_gem_metadatas = []
            final_gem_documents = []
            added_gem_keys = set() # Use a set of (name, level) tuples to track unique gems added

            targeted_added_count = 0
            # 1. Prioritize ALL targeted results (found by exact name match)
            logger.info("Prioritizing gems found by targeted name search...")
            for meta, doc in zip(targeted_gem_metadatas, targeted_gem_documents):
                gem_name = meta.get('name')
                gem_level = meta.get('level') # Handle potential missing level
                key = (gem_name, gem_level)
                if key not in added_gem_keys:
                    # Add targeted gems even if it exceeds MAX_CONTEXT_GEMS initially.
                    # We will truncate later if needed, but ensure these are considered first.
                    final_gem_metadatas.append(meta)
                    final_gem_documents.append(doc)
                    added_gem_keys.add(key)
                    targeted_added_count += 1
            logger.info(f"Added {targeted_added_count} unique gems from targeted search.")

            buff_query_added_count = 0
            # 2. Add buff query results (if applicable and space allows)
            if is_buff_query and buff_gem_metadatas and buff_gem_documents:
                logger.info("Adding gems from buff-specific search (if space allows)...")
                for meta, doc in zip(buff_gem_metadatas, buff_gem_documents):
                    if len(final_gem_metadatas) >= MAX_CONTEXT_GEMS:
                        logger.warning(f"Reached MAX_CONTEXT_GEMS ({MAX_CONTEXT_GEMS}) before adding all buff gems.")
                        break # Stop adding if limit is reached
                    gem_name = meta.get('name')
                    gem_level = meta.get('level')
                    key = (gem_name, gem_level)
                    if key not in added_gem_keys:
                        final_gem_metadatas.append(meta)
                        final_gem_documents.append(doc)
                        added_gem_keys.add(key)
                        buff_query_added_count += 1
                logger.info(f"Added {buff_query_added_count} unique gems from buff-specific search.")

            semantic_added_count = 0
            # 3. Add semantic results (if space allows)
            if semantic_gem_metadatas and semantic_gem_documents:
                logger.info("Adding gems from semantic search (if space allows)...")
                for meta, doc in zip(semantic_gem_metadatas, semantic_gem_documents):
                    if len(final_gem_metadatas) >= MAX_CONTEXT_GEMS:
                        logger.warning(f"Reached MAX_CONTEXT_GEMS ({MAX_CONTEXT_GEMS}) before adding all semantic gems.")
                        break # Stop adding if limit is reached
                    gem_name = meta.get('name')
                    gem_level = meta.get('level')
                    key = (gem_name, gem_level)
                    if key not in added_gem_keys:
                        final_gem_metadatas.append(meta)
                        final_gem_documents.append(doc)
                        added_gem_keys.add(key)
                        semantic_added_count += 1
                logger.info(f"Added {semantic_added_count} unique gems from semantic search.")

            # 4. Final Truncation (if targeted gems pushed over the limit)
            # If we have targeted gems, limit supplementary semantic gems to 5 to reduce dilution
            if targeted_added_count > 0:
                SEMANTIC_LIMIT = 5
                # Keep only targeted gems + up to SEMANTIC_LIMIT supplementary gems
                final_gem_metadatas = final_gem_metadatas[:targeted_added_count + SEMANTIC_LIMIT]
                final_gem_documents = final_gem_documents[:targeted_added_count + SEMANTIC_LIMIT]
                logger.info(f"Limited supplementary gems to {SEMANTIC_LIMIT} due to presence of targeted gems.")
            elif len(final_gem_metadatas) > MAX_CONTEXT_GEMS:
                logger.warning(f"Total gems ({len(final_gem_metadatas)}) exceeded MAX_CONTEXT_GEMS ({MAX_CONTEXT_GEMS}). Truncating.")
                final_gem_metadatas = final_gem_metadatas[:MAX_CONTEXT_GEMS]
                final_gem_documents = final_gem_documents[:MAX_CONTEXT_GEMS]

            logger.info(f"Final context gem count: {len(final_gem_metadatas)}")
            # --- End Merge Results ---

            # The subsequent code will use final_gem_metadatas and final_gem_documents
            # (Ensure the formatting loop below uses these variables correctly)

            # The subsequent code will use final_gem_metadatas and final_gem_documents
            # Ensure the formatting loop below uses these variables correctly.
            # (Checking original code: The next relevant part uses final_gem_metadatas and final_gem_documents, so variable names are consistent)
            # Note: Removed leftover lines from old merging logic here.
            # Removed confusing "Combined gem results" log message. The results are now selected, not combined.
            # --- End Determine Final Context ---

            formatted_gems = []  # Initialize before the check
            if final_gem_metadatas:
                # Build structured gem context with clear sections
                gem_context_sections = []

                # Add build goals at the top
                gem_context_sections.append(f"Build Goals: {build_goal}")

                # Add primary gem(s) section
                if targeted_added_count > 0:
                    primary_gem_entries = []
                    for meta, doc in zip(final_gem_metadatas[:targeted_added_count], final_gem_documents[:targeted_added_count]):
                        name = meta.get('name', 'Unknown Gem')
                        description = meta.get('description', '')
                        tags = meta.get('tags', '')
                        stats = meta.get('stats', '')
                        primary_gem_entries.append(
                            f"Primary Gem: {name}\nDescription: {description}\nTags: {tags}\nStats: {stats}\nDetails: {doc}"
                        )
                    gem_context_sections.append("\n\n".join(primary_gem_entries))

                # Add supplementary gems section
                if len(final_gem_metadatas) > targeted_added_count:
                    supplementary_entries = []
                    for meta, doc in zip(final_gem_metadatas[targeted_added_count:], final_gem_documents[targeted_added_count:]):
                        name = meta.get('name', 'Unknown Gem')
                        description = meta.get('description', '')
                        tags = meta.get('tags', '')
                        stats = meta.get('stats', '')
                        supplementary_entries.append(
                            f"Gem: {name}\nDescription: {description}\nTags: {tags}\nStats: {stats}\nDetails: {doc}"
                        )
                    gem_context_sections.append("Other Relevant Gems:\n" + "\n\n".join(supplementary_entries))

                # Join all sections into the final gem context string
                gem_context_str = "\n\n".join(gem_context_sections)

                # Log the final gem context for debugging
                logger.info(f"Final Gem Context Sent to LLM:\n{gem_context_str}")
            else:
                gem_context_str = "No relevant gem examples found."
                for meta, doc in zip(final_gem_metadatas, final_gem_documents):
                    # Create a structured multi-line entry including the full document content
                    meta = meta # Already have meta from the loop
                    doc = doc # Already have doc from the loop
                    name = meta.get('name', 'Unknown Gem')
                    level = meta.get('level', 'N/A') # Default if level is missing
                    # Ensure level from metadata is used if present
                    if 'level' in meta and meta['level'] is not None:
                        level = meta['level']
                    gem_type = meta.get('type', 'Unknown')
                    tags = meta.get('tags', '')

                    # Simplified gem entry format
                    formatted_gem_entry = (
                        f"- Name: {name}\n"
                        f"  Level: {level}\n"
                        f"  Tags: {tags}\n"
                        f"  Type: {gem_type}" # Using Type as Role proxy
                    )
                    formatted_gems.append(formatted_gem_entry)
                    # Note: 'doc' containing the full description is available here if needed later
                if formatted_gems:
                    # Update the context string label slightly to reflect combined results
                    gem_context_str = "Relevant gem examples (semantic + targeted) based on your query:\n" + "\n\n".join(formatted_gems) # Use double newline between gems
        else:
            logger.warning("Gems collection not available. Skipping gem context query.")

        # Combine general context
        other_context_parts = []
        if item_context_str and item_context_str != "No relevant item examples found.":
             other_context_parts.append(item_context_str)
        if passive_info_context_str and passive_info_context_str != "No relevant passive skill information found.":
             other_context_parts.append(passive_info_context_str)
        # Add gem context clearly labeled, including level if filtered
        if formatted_gems:
             level_info = f" at Level {requested_level}" if requested_level is not None else ""
             type_info = f" {requested_gem_type}" if requested_gem_type else ""
             gem_context_label = f"Relevant{type_info} Gems{level_info}"
             # Join with double newline for better separation between gem entries
             # Join the concise summary lines with single newlines
             # Join the structured multi-line entries with double newlines
             other_context_parts.append(f"{gem_context_label} based on your query:\n" + "\n\n".join(formatted_gems))
        elif gem_context_str: # Fallback if no gems found
             # Check if specific filters were applied but yielded no results
             if requested_gem_type or requested_level is not None:
                  other_context_parts.append(f"No matching {requested_gem_type or ''} gems found at level {requested_level or 'any'}.")
             else:
                  other_context_parts.append(gem_context_str) # Generic "no relevant gems"

        other_context_str = "\n\n".join(other_context_parts) if other_context_parts else "No additional context available."
        # Log the final context string before sending to LLM
        logger.debug(f"Final 'other_context_str' being sent to LLM:\n---\n{other_context_str}\n---")


        # Call the streaming LLM function with NO node-specific context
        logger.info("Calling streaming LLM function for general query.")
        general_context_note = "Context provides relevant items, passives, and gems based on the query."
        build_id = "default_build"  # TODO: replace with user/session/build-specific ID
        stream = get_llm_suggestion_stream(
            openai_client=openai_client,
            build_id=build_id,
            build_goal=build_goal,
            character_level=0,  # No specific character level context
            selected_nodes_list=None, # No selected nodes context
            accumulated_stats_str="N/A", # No accumulated stats context
            adjacent_options_formatted_list="N/A", # No adjacent nodes context
            other_context_str=other_context_str, # General context from DB queries
            item_context_str=item_context_str,
            passive_info_context_str=passive_info_context_str,
            gem_context_str=gem_context_str,
            selected_stats_str=selected_stats_str, # Pass selected stats
            user_query=user_query,
            passive_context_note=general_context_note, # Updated note
            history=history # Pass the parsed history
        )

        # Return a streaming response using Server-Sent Events (SSE)
        return Response(stream_with_context(stream), mimetype='text/event-stream')

    except Exception as e:
        logger.error(f"Error in /ask_general: {e}", exc_info=True) # Log traceback
        # Use a generator to yield the error as SSE
        def error_stream():
            yield f"data: {json.dumps({'error': 'An unexpected error occurred processing the general query.'})}\n\n"
        return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=500)


@app.route('/upload_image', methods=['POST'])
def upload_image():
    """Handles image file uploads."""
    logger.info("Received request for /upload_image")
    try:
        # Ensure upload directory exists
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        logger.info(f"Ensured upload directory exists: {UPLOAD_FOLDER}")

        # Check if the post request has the file part
        if 'image_file' not in request.files:
            logger.warning("No 'image_file' part in the request.")
            return jsonify({'error': 'No image file part in the request'}), 400

        files = request.files.getlist('image_file')
        if not files or all(f.filename == '' for f in files):
            logger.warning("No selected files submitted.")
            return jsonify({'error': 'No selected files'}), 400

        saved_filenames = []
        for file in files:
            if file and file.filename != '':
                filename = secure_filename(file.filename)
                save_path = UPLOAD_FOLDER / filename
                logger.info(f"Attempting to save file to: {save_path}")
                file.save(str(save_path))
                logger.info(f"Image successfully saved: {filename}")
                saved_filenames.append(filename)
            else:
                logger.warning("Skipped an empty or invalid file in the upload list.")

        if saved_filenames:
            return jsonify({'message': 'Images uploaded successfully', 'filenames': saved_filenames}), 200
        else:
            logger.warning("No valid files were uploaded.")
            return jsonify({'error': 'No valid files uploaded'}), 400

    except Exception as e:
        logger.error(f"Error during image upload: {e}", exc_info=True) # Log traceback
        return jsonify({'error': 'Failed to save image due to server error'}), 500


@app.route('/get_suggestion_with_image', methods=['POST']) # Changed to POST
def get_suggestion_with_image():
    """
    Receives a user query, build goal, and a list of image filenames.
    Reads the images, encodes them, sends them with text prompts to the multimodal LLM,
    and returns a streaming suggestion.
    """
    if not openai_client:
        logger.error("Server not fully initialized. Missing: OpenAI client.")
        def error_stream():
            yield f"data: {json.dumps({'error': 'Server initialization incomplete. Missing OpenAI client.'})}\n\n"
        return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=503)

    try:
        # Extract JSON data from request body
        data = request.get_json()
        if not data:
            logger.error("Received empty JSON data or non-JSON request body.")
            def error_stream(): yield f"data: {json.dumps({'error': 'No JSON data received or invalid Content-Type'})}\n\n"
            return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=400)

        # Extract parameters from JSON
        user_query = data.get('user_query')
        build_goal = data.get('build_goal', 'Analyze image') # Default goal if not provided
        image_filenames = data.get('image_filenames')  # Expecting a list of filenames now
        selected_nodes = data.get('selected_nodes', []) # Expecting a list
        selected_choices = data.get('selected_choices', {}) # Expecting a dict
        history = data.get('history', []) # Expecting a list of dicts

        # Check for required fields
        if not user_query:
            logger.error("Missing 'user_query' in JSON data.")
            def error_stream(): yield f"data: {json.dumps({'error': 'Missing user_query in JSON data'})}\n\n"
            return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=400)
        if not image_filenames or not isinstance(image_filenames, list):
            logger.error("Missing or invalid 'image_filenames' in JSON data.")
            def error_stream(): yield f"data: {json.dumps({'error': 'Missing or invalid image_filenames in JSON data'})}\n\n"
            return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=400)

        # Basic type validation (optional but good practice)
        if not isinstance(selected_nodes, list):
            if isinstance(selected_nodes, str):
                logger.warning("Received 'selected_nodes' as string, attempting second parse (potential double-encoding).")
                try:
                    selected_nodes = json.loads(selected_nodes)
                except (json.JSONDecodeError, TypeError):
                    logger.warning("Second parse failed for 'selected_nodes'.")
                    selected_nodes = None # Set to None to trigger the final check
            if not isinstance(selected_nodes, list):
                logger.warning(f"Final type check failed: 'selected_nodes' is not a list ({type(selected_nodes)}). Using empty list.")
                selected_nodes = []

        if not isinstance(selected_choices, dict):
            if isinstance(selected_choices, str):
                logger.warning("Received 'selected_choices' as string, attempting second parse (potential double-encoding).")
                try:
                    selected_choices = json.loads(selected_choices)
                except (json.JSONDecodeError, TypeError):
                    logger.warning("Second parse failed for 'selected_choices'.")
                    selected_choices = None
            if not isinstance(selected_choices, dict):
                logger.warning(f"Final type check failed: 'selected_choices' is not a dict ({type(selected_choices)}). Using empty dict.")
                selected_choices = {}

        if not isinstance(history, list):
            if isinstance(history, str):
                logger.warning("Received 'history' as string, attempting second parse (potential double-encoding).")
                try:
                    history = json.loads(history)
                except (json.JSONDecodeError, TypeError):
                    logger.warning("Second parse failed for 'history'.")
                    history = None
            if not isinstance(history, list):
                logger.warning(f"Final type check failed: 'history' is not a list ({type(history)}). Using empty list.")
                history = []

        # Read and encode all images
        base64_image_list = []
        image_type = 'jpeg'  # Default type
        for filename in image_filenames:
            try:
                secured_name = secure_filename(filename)
                image_path = UPLOAD_FOLDER / secured_name
                logger.info(f"Attempting to process image: {image_path}")
                with open(image_path, "rb") as image_file:
                    image_data = image_file.read()
                    base64_image_data = base64.b64encode(image_data).decode('utf-8')
                    base64_image_list.append(base64_image_data)
                # Detect image type from extension (use first image's type)
                ext = os.path.splitext(secured_name)[1].lower()
                if ext == '.png':
                    image_type = 'png'
                elif ext in ['.jpg', '.jpeg']:
                    image_type = 'jpeg'
                elif ext == '.gif':
                    image_type = 'gif'
                elif ext == '.webp':
                    image_type = 'webp'
            except FileNotFoundError:
                logger.error(f"Image file not found: {filename}")
                def error_stream(): yield f"data: {json.dumps({'error': f'Image file not found: {filename}'})}\n\n"
                return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=404)
            except Exception as e_read:
                logger.error(f"Error reading image file {filename}: {e_read}")
                def error_stream(): yield f"data: {json.dumps({'error': f'Error reading image file: {filename}'})}\n\n"
                return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=500)

        # Convert selected node IDs to full node dictionaries
        full_selected_nodes = []
        if isinstance(selected_nodes, list):
            for node_id in selected_nodes:
                node_data = all_nodes.get(node_id)
                if node_data is None:
                    node_data = all_nodes.get(str(node_id))
                if node_data:
                    full_selected_nodes.append(node_data)
                else:
                    logger.warning(f"Node ID '{node_id}' (type: {type(node_id)}) not found in all_nodes dictionary.")
        else:
            logger.error(f"selected_nodes was not a list after validation ({type(selected_nodes)}), cannot convert IDs.")
            full_selected_nodes = []

        # --- Calculate Accumulated Stats ---
        accumulated_stats_str = "Could not calculate accumulated stats."
        if full_selected_nodes and all_nodes:
            try:
                accumulated_stats_str = calculate_accumulated_stats(full_selected_nodes, all_nodes)
                logger.info("Successfully calculated accumulated stats for image suggestion.")
            except Exception as e_stats:
                logger.error(f"Error calculating accumulated stats for image suggestion: {e_stats}")
        elif not selected_nodes:
            accumulated_stats_str = "No nodes selected yet."

        # Call the multimodal LLM streaming function
        passive_nodes_str = ""
        if full_selected_nodes:
            passive_nodes_entries = []
            for node in full_selected_nodes:
                label = node.get('label', 'Unknown')
                choice = node.get('choice')
                description = node.get('description', '')
                choice_str = f" (Choice: {choice})" if choice else ""
                passive_nodes_entries.append(f"- {label}{choice_str}: {description}")
            passive_nodes_str = "Current Passive Nodes:\n" + "\n".join(passive_nodes_entries)
        else:
            passive_nodes_str = "No passive nodes selected yet."

        build_goal_with_nodes = f"{build_goal}\n\n{passive_nodes_str}"

        logger.info(f"Calling multimodal LLM function with {len(base64_image_list)} images and updated build goal and passive nodes context.")
        build_id = "default_build"  # TODO: replace with user/session/build-specific ID
        stream = get_llm_suggestion_with_image_stream(
            openai_client=openai_client,
            build_id=build_id,
            build_goal=build_goal_with_nodes,
            user_query=user_query,
            base64_image_list=base64_image_list,
            selected_nodes_list=full_selected_nodes,
            accumulated_stats_str=accumulated_stats_str,
            selected_choices_dict=selected_choices,
            image_type=image_type,
            history=history
        )

        return Response(stream_with_context(stream), mimetype='text/event-stream')

    except Exception as e:
        logger.error(f"Error in /get_suggestion_with_image: {e}", exc_info=True)
        def error_stream(): yield f"data: {json.dumps({'error': 'An unexpected error occurred processing the image suggestion request.'})}\n\n"
        return Response(stream_with_context(error_stream()), mimetype='text/event-stream', status=500)




# --- Run Server ---
if __name__ == '__main__':
    # Make sure 'data' directory exists before starting
    os.makedirs('data', exist_ok=True)
    # Use a different port if 5000 is common, e.g., 5001
    app.run(host='localhost', port=5001, debug=True) # Added debug=True for development
@app.route('/suggest_build_from_gear', methods=['POST'])
def suggest_build_from_gear():
    """
    Orchestrates gear image analysis and LLM build suggestion.
    Expects JSON body: { "filename": "...", "build_goals": "..." }
    Returns: Streaming LLM suggestion or error JSON.
    """
    data = request.get_json()
    if not data:
        return jsonify({'error': 'Missing JSON body.'}), 400

    filename = data.get('filename')
    build_goals = data.get('build_goals')

    if not filename or not isinstance(filename, str):
        return jsonify({'error': 'Missing or invalid "filename".'}), 400
    if not build_goals or not isinstance(build_goals, str):
        return jsonify({'error': 'Missing or invalid "build_goals".'}), 400

    image_path = os.path.join('data', 'uploads', filename)
    if not os.path.exists(image_path):
        return jsonify({'error': f'File not found: {filename}'}), 404

    try:
        gear_data = process_gear_image(image_path)
        if not gear_data or (isinstance(gear_data, dict) and gear_data.get('error')):
            return jsonify({'error': 'Failed to process gear image.'}), 500
    except Exception as e:
        return jsonify({'error': f'Exception during gear processing: {str(e)}'}), 500

    try:
        # Minimal context for LLM; pass gear_data and build_goals, others as None/empty
        llm_response_generator = get_llm_suggestion_stream(
            openai_client=None,
            build_id=None,
            build_goal=None,
            character_level=None,
            selected_nodes_list=None,
            accumulated_stats_str=None,
            adjacent_options_formatted_list=None,
            other_context_str=None,
            item_context_str=None,
            passive_info_context_str=None,
            gem_context_str=None,
            selected_stats_str=None,
            user_query=None,
            passive_context_note="",
            history=None,
            gear_data=gear_data,
            build_goals=build_goals
        )
    except Exception as e:
        return jsonify({'error': f'Exception during LLM suggestion: {str(e)}'}), 500

    return Response(stream_with_context(llm_response_generator), mimetype='text/event-stream')