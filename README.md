# PoE2 AI Builder
A modular tool to fetch Path of Exile 2 skill gems, support gems, and passive skill tree data, and use an LLM (via LM Studio) with Chroma vector DB to optimize character builds.

## Setup
1. Install dependencies: `pip install -r requirements.txt`
2. Run: `python main.py`

## Structure
- `data/`: Output files (gems, passive tree, LLM data, Chroma DB)
- `modules/`: Fetch, process, visualize, and LLM integration logic
- `main.py`: Orchestrates the build process

## Future Features
- Add armor and weapon data
- Integrate screenshot parsing for player stats