import json
import chromadb
from sentence_transformers import SentenceTransformer
import openai
import logging
import base64
import os
from chromadb.utils import embedding_functions
from modules.process_data import lookahead_pathfinding, score_node
from modules.context_manager import load_context
from modules.web_search import search_web

api_key = os.getenv("OPENAI_API_KEY")
if api_key:
    client = openai.OpenAI(api_key=api_key)
else:
    client = openai.OpenAI(api_key="not-needed", base_url="http://127.0.0.1:1234/v1")

logger = logging.getLogger(__name__)

# --- Conversation Alternation Utility ---
def enforce_strict_alternation(messages):
    """
    Given a list of messages (dicts with 'role' and 'content'), return a new list that strictly alternates
    user/assistant/user/assistant/... (system allowed only at the start). If multiple consecutive messages
    have the same role, only the last is kept. Logs any alternation issues or dropped messages.
    """
    import copy
    result = []
    last_role = None
    dropped = 0
    for i, msg in enumerate(messages):
        role = msg.get('role')
        if i == 0 and role == 'system':
            result.append(msg)
            last_role = 'system'
            continue
        if role == last_role:
            # Drop previous message of same role (keep only the last in a block)
            if len(result) > 0 and result[-1].get('role') == role:
                result[-1] = msg
                dropped += 1
            else:
                result.append(msg)
        else:
            result.append(msg)
        last_role = role
    # After system, must alternate user/assistant
    alternation_ok = True
    for i in range(1, len(result)):
        if result[i]['role'] == result[i-1]['role'] and result[i]['role'] != 'system':
            alternation_ok = False
            logger.warning(f"Alternation error at index {i}: {result[i-1]['role']} followed by {result[i]['role']}")
    if dropped > 0:
        logger.info(f"Dropped {dropped} consecutive same-role messages during alternation enforcement.")
    logger.debug(f"Final alternated message roles: {[m['role'] for m in result]}")
    return result

# --- Configuration ---
ITEM_DB_PATH = "data/chroma_db/"
ITEM_COLLECTION_NAME = "poe2_items"
ITEM_EMBEDDING_MODEL = 'all-MiniLM-L6-v2'
NUM_ITEM_SUGGESTIONS = 20

PASSIVE_DB_PATH = "data/chroma_db_build/"
PASSIVE_COLLECTION_NAME = "poe2_passives"
PASSIVE_EMBEDDING_MODEL = 'all-MiniLM-L6-v2'
NUM_PASSIVE_SUGGESTIONS = 20

# --- Helper function to format gear data for prompt inclusion ---
def format_gear_data(gear_data):
    if not gear_data:
        return ""
    if isinstance(gear_data, dict):
        return "\\n".join(f"{k}: {v}" for k, v in gear_data.items())
    elif isinstance(gear_data, list):
        return "\\n\\n".join(format_gear_data(item) for item in gear_data)
    else:
        return str(gear_data)
def _initialize_chroma_client(db_path):
    """Initializes and returns a persistent ChromaDB client."""
    try:
        os.makedirs(db_path, exist_ok=True)
        client = chromadb.PersistentClient(path=db_path)
        return client
    except Exception as e:
        logger.error(f"Failed to initialize ChromaDB client: {e}")
        raise

def _get_or_create_collection(client, collection_name, embedding_model_name):
    """Gets or creates a ChromaDB collection with the specified embedding function."""
    try:
        sentence_transformer_ef = embedding_functions.SentenceTransformerEmbeddingFunction(model_name=embedding_model_name)
        collection = client.get_or_create_collection(
            name=collection_name,
            metadata={"hnsw:space": "cosine", "embedding_function": "sentence_transformer"},
            embedding_function=sentence_transformer_ef
        )
        return collection
    except Exception as e:
        logger.error(f"Failed to get or create collection '{collection_name}': {e}")
        raise

def _query_chroma_collection(collection_name, query_texts, embedding_model, db_path, n_results=10, filters=None):
    """
    Helper to query a ChromaDB collection with semantic similarity.
    Returns documents and metadatas.
    """
    try:
        chroma_client = _initialize_chroma_client(db_path)
        collection = _get_or_create_collection(chroma_client, collection_name, embedding_model)
        results = collection.query(
            query_texts=query_texts,
            n_results=n_results,
            where=filters if filters else None
        )
        documents = results.get('documents', [[]])[0] if results else []
        metadatas = results.get('metadatas', [[]])[0] if results else []
        return documents, metadatas
    except Exception as e:
        logger.error(f"ChromaDB query failed for collection '{collection_name}': {e}")
        return [], []

def _load_persistent_context():
    """Load persistent build context from default_build.json."""
    try:
        with open("data/build_contexts/default_build.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load persistent context: {e}")
        return {}

def generate_query_with_llm(prompt, model="gemma-3-4b-it", temperature=0.1, max_tokens=4000):
    """
    Calls the local GEMMA-3 LLM (OpenAI-compatible API) to generate a query string from a prompt.
    Includes robust error handling and logging. Always includes persistent context.
    """
    try:
        persistent_context = _load_persistent_context()
        context_str = json.dumps(persistent_context, ensure_ascii=False, indent=2)
        full_prompt = (
            f"Context:\n{context_str}\n\n"
            f"Prompt:\n{prompt}\n"
            "Generate a concise, relevant query string for the above prompt and context."
        )
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "system", "content": "You are a helpful assistant for query generation."},
                      {"role": "user", "content": full_prompt}],
            temperature=temperature,
            max_tokens=max_tokens
        )
        reply = response.choices[0].message.content.strip()
        logger.info("LLM query generated successfully.")
        return reply
    except Exception as e:
        logger.error(f"LLM query generation failed: {e}")
        return ""

def query_relevant_items(query, item_type="item", n_results=10):
    """
    Query ChromaDB for relevant items, gems, or passives based on semantic similarity.
    item_type: 'item', 'gem', or 'passive'
    Returns a list of dicts with document and metadata.
    """
    try:
        if item_type == "item":
            collection_name = ITEM_COLLECTION_NAME
            db_path = ITEM_DB_PATH
            embedding_model = ITEM_EMBEDDING_MODEL
        elif item_type == "gem":
            collection_name = "poe2_gems"
            db_path = ITEM_DB_PATH
            embedding_model = ITEM_EMBEDDING_MODEL
        elif item_type == "passive":
            collection_name = PASSIVE_COLLECTION_NAME
            db_path = PASSIVE_DB_PATH
            embedding_model = PASSIVE_EMBEDDING_MODEL
        else:
            logger.error(f"Unknown item_type '{item_type}' for ChromaDB query.")
            return []

        documents, metadatas = _query_chroma_collection(
            collection_name=collection_name,
            query_texts=[query],
            embedding_model=embedding_model,
            db_path=db_path,
            n_results=n_results
        )
        results = []
        for doc, meta in zip(documents, metadatas):
            results.append({"document": doc, "metadata": meta})
        logger.info(f"ChromaDB returned {len(results)} results for type '{item_type}'.")
        return results
    except Exception as e:
        logger.error(f"query_relevant_items failed: {e}")
        return []

def generate_llm_build_data(
    skill_gems, support_gems, nodes, connections, class_start_nodes, path, playstyle, character_class, user_query=None,
    gear_data=None, build_goals=None
):
    """
    Use the LLM and ChromaDB to synthesize build data (summaries, suggestions) based on user input, context, and ChromaDB results.
    Always includes accumulated stats, selected nodes, build goals, and persistent context.
    Optionally includes user-provided build_goals and gear_data in the prompt.
    """
    try:
        # Load persistent context
        persistent_context = _load_persistent_context()
        # Use user-supplied build_goals if provided, else fallback to persistent context
        goals_section = build_goals if build_goals is not None else persistent_context.get("goals", [])
        selected_nodes = nodes
        # Load all_nodes for stats calculation
        with open("data/nodes_data.json", "r", encoding="utf-8") as f:
            all_nodes = json.load(f)
        from modules.process_data import calculate_accumulated_stats
        accumulated_stats = calculate_accumulated_stats(selected_nodes, all_nodes)
        # Query ChromaDB for relevant items, gems, passives
        item_results = query_relevant_items(user_query or "", item_type="item", n_results=10)
        gem_results = query_relevant_items(user_query or "", item_type="gem", n_results=10)
        passive_results = query_relevant_items(user_query or "", item_type="passive", n_results=10)
        # Compose prompt for LLM
        context_str = json.dumps({
            "build_goals": goals_section,
            "playstyle": playstyle,
            "character_class": character_class,
            "accumulated_stats": accumulated_stats,
            "selected_nodes": selected_nodes,
            "skill_gems": skill_gems,
            "support_gems": support_gems,
            "item_results": item_results,
            "gem_results": gem_results,
            "passive_results": passive_results,
            "persistent_context": persistent_context
        }, ensure_ascii=False, indent=2)

        # Format gear_data if provided
        gear_section = ""
        if gear_data:
            def format_gear(g):
                if isinstance(g, dict):
                    return "\n".join(f"{k}: {v}" for k, v in g.items())
                elif isinstance(g, list):
                    return "\n\n".join(format_gear(item) for item in g)
                else:
                    return str(g)
            gear_section = "\nUploaded Gear Analysis:\n" + format_gear(gear_data) + "\n"

        # Add build goals section if provided as string
        build_goals_section = ""
        if build_goals:
            build_goals_section = f"User's Build Goal: {build_goals}\n"

        prompt = (
            f"Given the following build context, user query, and semantic search results, "
            f"summarize the build, suggest improvements, and answer the user's query if present.\n"
            f"{build_goals_section}"
            f"Context:\n{context_str}\n"
            f"{gear_section}"
            f"User Query: {user_query or ''}\n"
            "Provide a structured JSON response with keys: summary, suggestions, and answer."
        )
        response = client.chat.completions.create(
            model="gemma-3-4b-it",
            messages=[{"role": "system", "content": "You are a Path of Exile 2 build assistant."},
                      {"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=4000
        )
        reply = response.choices[0].message.content.strip()
        # Try to parse as JSON, fallback to string
        try:
            build_data = json.loads(reply)
        except Exception:
            build_data = {"raw_response": reply}
        logger.info("LLM build data generated successfully.")
        return build_data
    except Exception as e:
        logger.error(f"generate_llm_build_data failed: {e}")
        return {"error": str(e)}

def store_in_chroma(llm_data, collection_type="build_summaries"):
    """
    Store LLM-generated data in the appropriate ChromaDB collection.
    collection_type: e.g., 'build_summaries', 'poe2_items', etc.
    """
    try:
        db_path = ITEM_DB_PATH if collection_type != "poe2_passives" else PASSIVE_DB_PATH
        embedding_model = ITEM_EMBEDDING_MODEL if collection_type != "poe2_passives" else PASSIVE_EMBEDDING_MODEL
        chroma_client = _initialize_chroma_client(db_path)
        collection = _get_or_create_collection(chroma_client, collection_type, embedding_model)
        # Prepare document and metadata
        doc = json.dumps(llm_data, ensure_ascii=False)
        meta = {"source": "llm", "type": collection_type}
        collection.add(documents=[doc], metadatas=[meta])
        logger.info(f"LLM data stored in ChromaDB collection '{collection_type}'.")
        return True
    except Exception as e:
        logger.error(f"store_in_chroma failed: {e}")
        return False

def get_llm_suggestion_stream(
    openai_client, build_id, build_goal, character_level, selected_nodes_list, accumulated_stats_str,
    adjacent_options_formatted_list, other_context_str, item_context_str, passive_info_context_str, gem_context_str,
    selected_stats_str, user_query, passive_context_note="", history=None, gear_data=None, build_goals=None
):
    """
    Calls the LLM with streaming enabled and yields response chunks.
    """
    if not openai_client:
        logger.error("OpenAI client not provided to get_llm_suggestion_stream.")
        yield "data: {\"error\": \"LLM client not initialized.\"}\n\n"
        return

    # --- Dynamic Semantic Item Retrieval ---
    try:
        chroma_client = _initialize_chroma_client(ITEM_DB_PATH)
        collection = _get_or_create_collection(chroma_client, ITEM_COLLECTION_NAME, ITEM_EMBEDDING_MODEL)
        results = collection.query(
            query_texts=[user_query],
            n_results=NUM_ITEM_SUGGESTIONS
        )
        item_docs = results.get('documents', [[]])[0] if results else []
        metadatas = results.get('metadatas', [[]])[0] if results else []

        # Define passive-related keywords
        passive_keywords = [
            "passive", "passive skill", "passive skills", "passive node", "passive nodes",
            "passive point", "passive points", "passive tree", "point skill", "point skills"
        ]
        user_query_lower = user_query.lower()
        is_passive_query = any(kw in user_query_lower for kw in passive_keywords)

        # If the user query relates to passives, filter to only passive skill nodes
        if is_passive_query:
            filtered = [
                (doc, meta) for doc, meta in zip(item_docs, metadatas)
                if meta.get('type') == 'passive_node'
            ]
            if filtered:
                item_docs, metadatas = zip(*filtered)
                item_docs = list(item_docs)
                metadatas = list(metadatas)
            else:
                # No matching passive nodes found, clear results
                item_docs = []
                metadatas = []

        # Detect item type dynamically from user query
        item_types = [
            "crossbow", "bow", "sword", "axe", "helmet", "armor", "shield",
            "amulet", "ring", "boots", "gloves", "dagger", "mace", "staff",
            "wand", "quiver", "belt", "body armour", "chest", "gauntlets",
            "greaves", "cap", "crown", "mask", "circlet", "robe", "tunic"
        ]
        detected_type = None
        for item_type in item_types:
            if item_type in user_query_lower:
                detected_type = item_type
                break

        if detected_type:
            filtered_items = [
                (doc, meta) for doc, meta in zip(item_docs, metadatas)
                if meta.get('type') == detected_type
            ]
            # Exclude unrelated types like skills and passives explicitly
            filtered_items = [
                (doc, meta) for doc, meta in filtered_items
                if meta.get('type') not in ('skill', 'passive')
            ]
            if filtered_items:
                item_docs, metadatas = zip(*filtered_items)
                item_docs = list(item_docs)
                metadatas = list(metadatas)
            else:
                # No matching items of detected type found, clear results
                item_docs = []
                metadatas = []
            # Prepend explicit instruction to the item context string
            item_context_str = f"Only consider items of type '{detected_type}'. Ignore skills and passives.\n" + item_context_str

        # --- Web Search Context: Always include web search for every user_query ---
        try:
            web_context_str = search_web(user_query)
            if not web_context_str or not web_context_str.strip():
                web_context_str = "No relevant web summary found."
        except Exception as e:
            logger.warning(f"Web search failed: {e}")
            web_context_str = "No relevant web summary found."
        # Optional: Filter or re-rank using metadata
        # Example: prioritize certain item types or tags (placeholder)
        # filtered_items = []
        # for doc, meta in zip(item_docs, metadatas):
        #     if meta.get('type') in preferred_types:
        #         filtered_items.append((doc, meta))
        # Use item_docs directly for now

        if item_docs:
            formatted_items = []
            for doc in item_docs:
                formatted_items.append(f"- {doc.strip()}")
            item_context = "\n".join(formatted_items)
            item_context_str = f"Relevant Items:\n{item_context}"
            logger.info(f"Injected {len(item_docs)} items into item_context_str.")
        else:
            logger.info("No relevant items found in ChromaDB query.")
    except Exception as e:
        logger.warning(f"Dynamic item retrieval failed: {e}")
    # --- End Dynamic Semantic Item Retrieval ---
    # --- Passive Skills Retrieval with Combined Query ---
    try:
        chroma_client_passive = _initialize_chroma_client(PASSIVE_DB_PATH)
        collection_passive = _get_or_create_collection(chroma_client_passive, PASSIVE_COLLECTION_NAME, PASSIVE_EMBEDDING_MODEL)

        combined_queries = [user_query, build_goal]
        passive_results_combined = collection_passive.query(
            query_texts=combined_queries,
            n_results=NUM_PASSIVE_SUGGESTIONS
        )
        passive_docs_combined = passive_results_combined.get('documents', [[]])[0] if passive_results_combined else []
        passive_metadatas_combined = passive_results_combined.get('metadatas', [[]])[0] if passive_results_combined else []

        if passive_docs_combined:
            detailed_passives = []
            for meta in passive_metadatas_combined:
                name = meta.get('name', 'Unknown')
                effect = meta.get('effect', 'No effect info')
                stats = meta.get('stats', 'No stats info')
                detailed_passives.append(f"- Name: {name}\n  Effect: {effect}\n  Stats: {stats}")
            detailed_passives_str = "\n".join(detailed_passives)
            other_context_str += f"\n\n--- Relevant Passive Nodes ---\n{detailed_passives_str}\n--- End Passive Nodes ---"
            logger.info(f"Injected {len(detailed_passives)} detailed passive nodes into other_context_str.")
        else:
            logger.info("No detailed passive nodes found in combined query.")

        # Handle empty adjacent nodes by injecting curated passives
        if is_passive_query and not adjacent_options_formatted_list.strip():
            curated_passives = []
            for meta in passive_metadatas_combined:
                name = meta.get('name', 'Unknown')
                effect = meta.get('effect', 'No effect info')
                stats = meta.get('stats', 'No stats info')
                curated_passives.append(f"- Name: {name}\n  Effect: {effect}\n  Stats: {stats}")
            curated_passives_str = "\n".join(curated_passives)
            adjacent_options_formatted_list = f"No adjacent nodes found. Here are some relevant passive nodes matching your build goal:\n{curated_passives_str}"

    except Exception as e:
        logger.warning(f"Passive skills retrieval failed: {e}")

    # Suppress unrelated gem/item/passive info context if passive query
    if is_passive_query:
        item_context_str = ""
        gem_context_str = ""
        passive_info_context_str = ""

    # Enrich other_context_str with persistent context snippet
    try:
        from modules.context_manager import get_context_snippet
        persistent_context = get_context_snippet(build_id)
        if persistent_context:
            other_context_str = f"{other_context_str}\n\n--- Persistent Build Context ---\n{persistent_context}\n--- End Persistent Context ---"
    except Exception as e:
        logger.warning(f"Could not load persistent context for build_id {build_id}: {e}")

    # --- Lookahead Pathfinding for Future Passive Suggestions ---
    try:
        import json as _json
        with open("data/nodes_data.json", "r", encoding="utf-8") as f:
            all_nodes = _json.load(f)
        with open("data/connections_data.json", "r", encoding="utf-8") as f:
            connections = _json.load(f)

        allocated_ids = [str(node.get('id')) for node in selected_nodes_list if 'id' in node]
        allocated_set = set(allocated_ids)

        # Extract key stats from build goal (placeholder: use build_goal string directly)
        key_stats = build_goal

        # Lookahead depth configurable
        max_depth = 3
        future_nodes_paths = lookahead_pathfinding(allocated_ids, all_nodes, connections, max_depth=max_depth, allocated_set=allocated_set)

        scored_future_nodes = []
        for node_id, path in future_nodes_paths.items():
            node_data = all_nodes.get(node_id, {})
            if score is None:
                score = 0.0
            score = score_node(node_data, key_stats)
            scored_future_nodes.append((score, node_id, node_data, path))

        # Sort by score descending
        scored_future_nodes.sort(reverse=True, key=lambda x: x[0])

        # Format top 10
        future_path_suggestions_formatted = ""
        for score, node_id, node_data, path in scored_future_nodes[:10]:
            name = node_data.get('name', 'Unknown')
            stats = node_data.get('stats', 'No stats')
            future_path_suggestions_formatted += f"- {name} (ID: {node_id}, Score: {score:.2f}): {stats}\\n  Path: {' -> '.join(path)}\\n"

        if not future_path_suggestions_formatted:
            future_path_suggestions_formatted = "No promising future nodes found within lookahead depth."

    except Exception as e:
        logger.warning(f"Lookahead pathfinding failed: {e}")
        future_path_suggestions_formatted = "Lookahead pathfinding unavailable."

    # --- Load and format build context JSON ---
    try:
        with open("data/build_contexts/default_build.json", "r", encoding="utf-8") as f:
            build_context_json = json.load(f)
        build_context_str = "--- Build Context JSON ---\n" + json.dumps(build_context_json, indent=2, ensure_ascii=False) + "\n--- End Build Context ---"
        logger.info("Included build context JSON in user message for LLM call.")
    except Exception as e:
        build_context_str = "--- Build Context JSON ---\n[Error loading build context: {}]\n--- End Build Context ---".format(e)
        logger.warning(f"Failed to load build context JSON: {e}")

    # Initialize messages with the system prompt
    messages = [
        {
            "role": "system",
            "content": """You are a Path of Exile 2 build assistant. Your task is to answer the user's query based *strictly* on the provided context sections: User Goal, Character Level, Gem Context, Accumulated Stats, Adjacent Node Options, Other Context (Items, Passives), and (if present) Web Search Context. If conversation history is provided, consider it as part of the ongoing interaction.

**Pay close attention to the 'User Goal' provided in the user's message context and tailor your recommendations accordingly.**

When recommending the next passive node(s) or gear to allocate, you MUST:
- Carefully review all options listed in the '--- Directly Reachable Next Nodes (Adjacent & Available) ---' section, including their names, effects, and stats. Only consider nodes that are unallocated and adjacent.
- Compare these options in the context of the user's build goals, accumulated stats, and any relevant web search or image context.
- Clearly recommend the best next node(s) or gear to take, explaining your reasoning based on the provided context.
- If multiple nodes or gear are strong choices, explain the pros and cons of each and indicate which is most synergistic with the user's goals and current build.
- Never suggest nodes that have already been selected or allocated.

If a 'Web Search Context' or image context section is provided, you may use it to supplement your answer, but still prioritize the provided build/item/passive context.

**CRITICAL RULES:**
1.  **Base answers ONLY on the provided context AND conversation history.** Do not use external knowledge or make assumptions. Trust the information given.
2.  **If the context/history does not contain the answer, explicitly state that.** Do not guess or fabricate.
3.  **Do not invent or hallucinate information.**
4.  **Gem Information:** You **MUST** use the information in the `--- Gem Context ---` section to answer questions about gems. Available gems are listed there. Strictly adhere to the 'Type' (Skill/Support) and 'Level' provided.
5.  **Level Constraint:** If the query specifies a level and the context label reflects this, only suggest gems matching that level.
6.  When recommending a skill node, use the exact `Node Name` and `Node Effect`.
7.  Explain reasoning clearly, referencing specific context/history details. Synthesize all provided gem fields (Name, Level, Type, Tags, Description) to understand function.
8.  When explaining a gem's function or effect, **directly quote or paraphrase closely from the text provided under the 'Details:' section** for that gem in the context. Avoid overly simplifying or relying on external knowledge.
9.  **DO NOT suggest or mention ANY gems, items, passive nodes, or other game elements that are NOT explicitly listed in the `--- Gem Context ---`, `--- Other Context ---`, or `--- Adjacent Node Options ---` sections provided for THIS specific query.** Your entire response must be based SOLELY on the information given in the current context."""
        }
    ]

    # Conversation history merging and alternation removed per refactor instructions.

    # Add user message (selected nodes section removed, accumulated stats section added)
    messages.append(
        {
            "role": "user",
            "content": f'''**Current Build Context & Query:**
             
User Goal: {build_goals if build_goals is not None else build_goal}
{build_context_str}
Character Level: {character_level} (Points already allocated)

--- Gem Context ---
{gem_context_str}
---

Accumulated Stats from Selected Nodes:
{accumulated_stats_str}

--- Current Build Stats (from selected passives) ---
{f"No passive stats selected yet." if not selected_stats_str else selected_stats_str.replace('<p>', '').replace('</p>', '\\n').strip()}
--- End Current Build Stats ---

--- Directly Reachable Next Nodes (Adjacent & Available) ---
{adjacent_options_formatted_list}
---

--- Future Passive Node Path Suggestions ---
{future_path_suggestions_formatted}
---

--- Other Context (Items, Passives, Other Nodes) ---
{other_context_str}
---

--- Item Context ---
{item_context_str}
---
{f"--- Uploaded Gear Analysis ---\n{format_gear_data(gear_data)}\n---" if gear_data else ""}

--- Web Search Context ---
{web_context_str}
---

**User Query:** {user_query}

---
Based *strictly* on the conversation history (if any) and the context sections provided above, answer the User Query.

**REMEMBER THE CRITICAL RULES:**
*   **Answer ONLY from the context and history.**
*   **If the answer isn't available, state that clearly.**
*   **DO NOT HALLUCINATE.** Use exact names, types, levels, and effects from context.
*   **Gem Handling:** You **MUST** use the `--- Gem Context ---` section for all gem information. Ensure suggested gems match requested type (Skill/Support) AND level (if specified).
    *   Explain your reasoning by referencing **all details** provided for the gem in the `--- Gem Context ---` (Name, Level, Type, Tags, Details). **Quote or use the exact wording from the 'Details:' section when describing what a gem does.**
    *   **Final Check:** Ensure your response ONLY discusses elements found within the provided context sections (`--- Gem Context ---`, `--- Other Context ---`, `--- Item Context ---`, `--- Adjacent Node Options ---`). Do not suggest external or unlisted gems/items.
Answer the query now:'''
        }
    )

    try:
        logger.info(f"Sending messages to LLM: {json.dumps(messages, ensure_ascii=False, indent=2)}")
        stream = openai_client.chat.completions.create(
            messages=messages,
            model="gemma-3-4b-it",
            temperature=0.6,
            max_tokens=8024,
            stream=True
        )
        full_reply = ""
        for chunk in stream:
            if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                full_reply += content
                sse_data = json.dumps({"content": content})
                yield f"data: {sse_data}\n\n"
        logger.info("LLM stream finished.")

        # Save user query and assistant reply to context history
        try:
            build_context_path = "data/build_contexts/default_build.json"
            with open(build_context_path, "r", encoding="utf-8") as f:
                build_context = json.load(f)
            if "history" not in build_context or not isinstance(build_context["history"], list):
                build_context["history"] = []

            # Append user query
            build_context["history"].append({
                "role": "user",
                "content": user_query
            })

            # Append assistant reply
            build_context["history"].append({
                "role": "assistant",
                "content": full_reply
            })

            # Update current_stats in build context
            build_context["current_stats"] = {
                "accumulated_stats": accumulated_stats_str,
                "selected_stats": selected_stats_str
            }

            with open(build_context_path, "w", encoding="utf-8") as f:
                json.dump(build_context, f, indent=2, ensure_ascii=False)
            logger.info("Appended user query and assistant reply to build context history.")
        except Exception as e:
            logger.error(f"Failed to append conversation to build context: {e}")
    except openai.OpenAIError as e:
        logger.error(f"LLM Connection Error (Stream): {e}")
        sse_error = json.dumps({"error": "Could not connect to the LLM inference server."})
        yield f"data: {sse_error}\n\n"
    except Exception as e:
        logger.error(f"Unexpected error during LLM stream: {e}")
        sse_error = json.dumps({"error": "An unexpected error occurred while communicating with the LLM."})
        yield f"data: {sse_error}\n\n"

def _is_calculation_request(user_query):
    """
    Detects if the user query is a calculation request based on keywords/phrases.
    """
    if not isinstance(user_query, str):
        return False
    calc_keywords = [
        "calculate", "calculation", "how much", "dps", "damage per second", "total", "sum", "average",
        "what is the", "add up", "multiplied", "divided", "minus", "difference", "combined", "net", "result",
        "expected", "output", "final value", "stat total", "stat sum", "stat average", "stat calculation"
    ]
    user_query_lower = user_query.lower()
    return any(kw in user_query_lower for kw in calc_keywords)

def get_llm_suggestion_with_image_stream(
    openai_client, build_id, build_goal, user_query, base64_image_list, selected_nodes_list, accumulated_stats_str,
    selected_choices_dict, image_type='jpeg', history=None, gear_data=None, build_goals=None
):
    """
    Sends a multimodal prompt (text + multiple images) to the local OpenAI-compatible API and streams the response.
    """
    import json
    # Load node data for passive/adjacent node info
    try:
        with open("data/nodes_data.json", "r", encoding="utf-8") as f:
            nodes_data = json.load(f)
    except Exception as e:
        nodes_data = {}
        logger.warning(f"Failed to load nodes_data.json: {e}")
    
    # Prepare the list of base64 image URLs
    image_entries = []
    if isinstance(base64_image_list, list):
        for base64_image_data in base64_image_list:
            image_url = f"data:image/{image_type};base64,{base64_image_data}"
            image_entries.append({"type": "image_url", "image_url": {"url": image_url}})
    else:
        # Backward compatibility: if a single string is passed, wrap it as list
        image_url = f"data:image/{image_type};base64,{base64_image_list}"
        image_entries.append({"type": "image_url", "image_url": {"url": image_url}})

    is_calc_request = _is_calculation_request(user_query)

    # Build the system prompt
    if is_calc_request:
        system_prompt = (
            "When the user requests a calculation, carefully examine all numerical values and their associated labels present in the provided OCR text for all relevant images. "
            "Identify which of these numerical stats are relevant to the user's specific calculation request. For example, if the user asks for DPS, consider stats such as average damage, attack time or cooldown, projectiles fired per use, chance to activate a second time, and any other numbers that could affect the calculation. "
            "For other calculations (such as total damage, effective health, etc.), consider all relevant stats such as main hand damage ranges, modifiers, or any other applicable values.\n\n"
            "Use all relevant numerical stats extracted from the OCR, along with any relevant 'Accumulated Stats', to perform the requested calculation as accurately as possible. "
            "Clearly state the calculation you performed, including which stats were used and how they were combined, and provide the final result.\n\n"
            "Strictly forbidden: Do not provide build advice, do not suggest nodes, and do not discuss strategy or optimization. Only perform the requested calculation and present the result."
        )
    else:
        system_prompt = (
            "You are an expert Path of Exile 2 build assistant. "
            "For each image, you are provided both the image and the OCR-extracted text. "
            "Use the OCR text as the authoritative source for any calculations or stat-based answers. "
            "If the user asks for a calculation (e.g., DPS), use the numbers and stats from the OCR text. "
            "Only use web search context if the answer cannot be determined from the provided images and OCR text. "
            "Clearly reference which image/OCR text you are using in your answer. "
            "Compare the options shown in the provided images and recommend the best one for the user's build goals. "
            "Use all available context: build context, accumulated stats, adjacent node options, text extracted from images, and web search results. "
            "You may use the web search context to supplement your reasoning, but do not hallucinate or fabricate information. "
            "If the answer is not present in the images, build context, or web search context, explicitly state that you do not have enough information. "
            "Base your response on the provided build context, accumulated stats, adjacent node options, image content, and web search context. "
            "Never suggest nodes that have already been selected or allocated. Only consider unallocated, adjacent nodes for next node suggestions."
        )
    
    # --- Load and format build context JSON ---
    try:
        with open("data/build_contexts/default_build.json", "r", encoding="utf-8") as f:
            build_context_json = json.load(f)
        build_context_str = "--- Build Context JSON ---\n" + json.dumps(build_context_json, indent=2, ensure_ascii=False) + "\n--- End Build Context ---"
        logger.info("Included build context JSON in user message for LLM call.")
    except Exception as e:
        build_context_str = "--- Build Context JSON ---\n[Error loading build context: {}]\n--- End Build Context ---".format(e)
        logger.warning(f"Failed to load build context JSON: {e}")

    # --- Format Directly Reachable Next Nodes (Adjacent & Available) ---
    adjacent_node_ids = set()
    if selected_choices_dict:
        for v in selected_choices_dict.values():
            # v can be a node id, list of node ids, or dict with node id
            if isinstance(v, (str, int)):
                adjacent_node_ids.add(str(v))
            elif isinstance(v, list):
                for nid in v:
                    adjacent_node_ids.add(str(nid))
            elif isinstance(v, dict):
                # Try to extract node id from dict
                node_id = v.get("id") or v.get("node_id")
                if node_id:
                    adjacent_node_ids.add(str(node_id))
    adjacent_nodes_lines = []
    for node_id in sorted(adjacent_node_ids):
        node = nodes_data.get(node_id)
        if node:
            name = node.get("name", node_id)
            stats = node.get("stats", "")
            adjacent_nodes_lines.append(f"{name}: {stats}")
        else:
            adjacent_nodes_lines.append(f"{node_id}: [Unknown Node]")
    adjacent_nodes_str = "--- Directly Reachable Next Nodes (Adjacent & Available) ---\n"
    if adjacent_nodes_lines:
        adjacent_nodes_str += "\n".join(adjacent_nodes_lines)
    else:
        adjacent_nodes_str += "[None]"
    adjacent_nodes_str += "\n--- End Directly Reachable Next Nodes ---"
    
    # --- Build web search context for each image ---
    web_search_context_sections = []
    if isinstance(base64_image_list, list):
        for idx, base64_image_data in enumerate(base64_image_list):
            # Try to get image text from selected_choices_dict (by index or key)
            image_text = None
            if selected_choices_dict:
                # Try by index as string, then as int, then fallback to None
                image_text = selected_choices_dict.get(str(idx)) or selected_choices_dict.get(idx)
            if not image_text:
                image_text = f"Image {idx+1}"
            # Perform web search
            web_search_results = search_web(image_text, max_results=3)
            section = f"--- Web Search Context for {image_text} ---\n{web_search_results}\n--- End Web Search Context ---"
            web_search_context_sections.append({"type": "text", "text": section})
    else:
        # Single image case
        image_text = None
        if selected_choices_dict:
            image_text = selected_choices_dict.get("0") or selected_choices_dict.get(0)
        if not image_text:
            image_text = "Image 1"
        web_search_results = search_web(image_text, max_results=3)
        section = f"--- Web Search Context for {image_text} ---\n{web_search_results}\n--- End Web Search Context ---"
        web_search_context_sections.append({"type": "text", "text": section})

    # --- Build image/OCR blocks for user message ---
    image_ocr_blocks = []
    if isinstance(base64_image_list, list):
        for idx, base64_image_data in enumerate(base64_image_list):
            image_url = f"data:image/{image_type};base64,{base64_image_data}"
            ocr_text = ""
            if selected_choices_dict:
                ocr_text = selected_choices_dict.get(str(idx)) or selected_choices_dict.get(idx) or ""
            image_ocr_blocks.extend([
                {"type": "text", "text": f"--- Image {idx+1} ---"},
                {"type": "image_url", "image_url": {"url": image_url}},
                {"type": "text", "text": f"--- Image {idx+1} OCR Text ---\n{ocr_text}\n--- End Image {idx+1} ---"}
            ])
    else:
        # Single image case
        image_url = f"data:image/{image_type};base64,{base64_image_list}"
        ocr_text = ""
        if selected_choices_dict:
            ocr_text = selected_choices_dict.get("0") or selected_choices_dict.get(0) or ""
        image_ocr_blocks.extend([
            {"type": "text", "text": f"--- Image 1 ---"},
            {"type": "image_url", "image_url": {"url": image_url}},
            {"type": "text", "text": f"--- Image 1 OCR Text ---\n{ocr_text}\n--- End Image 1 ---"}
        ])

    # --- Build the user message content conditionally ---
    user_message_content = []
    if is_calc_request:
        # Calculation context: images/OCR, accumulated stats, and the user query (omit build_goal, build_context, adjacent nodes, web search, etc.)
        user_message_content.extend(image_ocr_blocks)
        user_message_content.append({"type": "text", "text": f"Accumulated Stats:\n{accumulated_stats_str}\n"})
        user_message_content.append({"type": "text", "text": f"Calculation request: {user_query}"})
    else:
        # Full context as before
        user_message_content.extend(image_ocr_blocks)
        user_message_content.append({"type": "text", "text": build_context_str})
        user_message_content.append({"type": "text", "text": f"Accumulated Stats from Selected Nodes:\n{accumulated_stats_str}\n"})
        user_message_content.append({"type": "text", "text": adjacent_nodes_str})
        user_message_content.append({"type": "text", "text": f"Build goal: {build_goal}\n\nQuestion: {user_query}"})
        # Add web search context at the end, labeled as fallback
        for section in web_search_context_sections:
            # Add a fallback label to the web search context
            fallback_text = section["text"]
            if not fallback_text.startswith("--- Web Search Context (Fallback) ---"):
                fallback_text = fallback_text.replace("--- Web Search Context", "--- Web Search Context (Fallback)")
            user_message_content.append({"type": "text", "text": fallback_text})

    # Insert build goals and gear data sections if provided
    if build_goals:
        user_message_content.insert(0, {"type": "text", "text": f"User's Build Goal: {build_goals}"})
    if gear_data:
        formatted_gear = format_gear_data(gear_data)
        user_message_content.append({"type": "text", "text": f"--- Uploaded Gear Analysis ---\n{formatted_gear}\n---"})

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_message_content}
    ]
    
    # Log the full messages list being sent to the LLM
    logger.info(f"Sending messages to LLM: {json.dumps(messages, ensure_ascii=False, indent=2)}")
    
    try:
        response = openai_client.chat.completions.create(
            model="gemma-3-4b-it",  # or your multimodal model name
            messages=messages,
            stream=True,
            max_tokens=4000,
            temperature=0.6,
            top_p=1
        )
    
        full_reply = ""
    
        for chunk in response:
            delta = chunk.choices[0].delta
            if hasattr(delta, 'content') and delta.content:
                full_reply += delta.content
                data = {"content": delta.content}
                yield f"data: {json.dumps(data)}\n\n"
    
        # After streaming completes, save user query and assistant reply to context history
        try:
            build_context_path = "data/build_contexts/default_build.json"
            with open(build_context_path, "r", encoding="utf-8") as f:
                build_context = json.load(f)
    
            if "history" not in build_context or not isinstance(build_context["history"], list):
                build_context["history"] = []
    
            # Append user query
            build_context["history"].append({
                "role": "user",
                "content": user_query
            })
    
            # Append assistant reply
            build_context["history"].append({
                "role": "assistant",
                "content": full_reply
            })
    
            with open(build_context_path, "w", encoding="utf-8") as f:
                json.dump(build_context, f, indent=2, ensure_ascii=False)
    
        except Exception as e:
            # Log or ignore persistence errors silently
            pass
    
    except Exception as e:
        error_data = {"content": f"Image-based suggestion failed: {str(e)}"}
        yield f"data: {json.dumps(error_data)}\n\n"
