import os
import pathlib
import collections # Added for Counter
from collections import defaultdict
import heapq
import re # Add regex import

MAX_POINTS = 123

# Dictionary mapping generic build focuses to relevant stat keywords
FOCUS_KEYWORDS = {
    "Damage": [
        "damage", "attack", "spell", "elemental", "physical", "chaos",
        "speed", "critical", "penetration", "area", "projectile", "dot",
        "mine", "trap", "minion", "brand", "totem", "accuracy", "ailment"
    ],
    "Defense": [
        "life", "armour", "evasion", "energy shield", "resistance", "block",
        "dodge", "fortify", "guard", "ward", "recovery", "leech", "regeneration"
    ],
    # Add other focuses as needed
}

def select_skill_gem(skill_gems, playstyle="Melee Damage"):
    for gem in skill_gems:
        if playstyle.lower() in gem["primary_role"].lower():
            return gem
    return skill_gems[0] if skill_gems else None

def select_support_gems(support_gems, skill_gem, max_support=3):
    selected = []
    skill_tags = set(skill_gem["tags"].split(", "))
    for gem in support_gems:
        support_tags = set(gem["tags"].split(", "))
        if skill_tags & support_tags:
            selected.append(gem)
            if len(selected) >= max_support:
                break
    return selected

def identify_key_stats(skill_gem):
    tags = skill_gem["tags"].split(", ")
    key_stats = []
    if "Attack" in tags:
        key_stats.extend(["attack damage", "attack speed", "melee damage"])
    if "Melee" in tags:
        key_stats.extend(["melee damage", "strength"])
    if "Spell" in tags:
        key_stats.extend(["spell damage", "cast speed", "intelligence"])
    if "Fire" in tags:
        key_stats.append("fire damage")
    if "Cold" in tags:
        key_stats.append("cold damage")
    if "Lightning" in tags:
        key_stats.append("lightning damage")
    if "Chaos" in tags:
        key_stats.append("chaos damage")
    if "AoE" in tags:
        key_stats.append("area damage")
    if "Projectile" in tags:
        key_stats.append("projectile damage")
    if "Grenade" in tags:
        key_stats.append("grenade damage")
    key_stats.extend(["life", "mana", "resistance"])
    return key_stats

def score_node(node, key_stats):
    """Scores a passive skill node based on its relevance to key stats."""
    score = 0
    # Get stats value and ensure it's a string, defaulting to ""
    stats_val = node.get("stats")
    if stats_val is None:
        stats_val = ""
    elif not isinstance(stats_val, str):
        try:
            stats_val = str(stats_val) # Convert non-strings (like numbers)
        except:
            stats_val = "" # Fallback if conversion fails
    
    node_stats_lower = stats_val.lower() # Now safe to call lower()

    if not node_stats_lower: # Skip nodes with no stats text
        return 0

    for keyword in key_stats:
        keyword_lower = keyword.lower() # Convert keyword to lowercase
        # Check if the lowercase keyword is in the lowercase node stats
        # Simple containment check is often sufficient for PoE stats
        if keyword_lower in node_stats_lower:
            score += 10 # Base score for matching a keyword

    # Add bonus scores for important node types
    if node.get("is_keystone", False):
        score += 50 # Significant bonus for keystones
    elif node.get("is_notable", False):
        score += 20 # Moderate bonus for notables


def lookahead_pathfinding(allocated_node_ids, all_nodes, connections, max_depth=3, allocated_set=None):
    """
    From allocated nodes, explore up to max_depth steps ahead.
    Return dict of unallocated reachable nodes with minimal paths.
    """
    if allocated_set is None:
        allocated_set = set(allocated_node_ids)

    visited = set(allocated_node_ids)
    queue = []
    future_nodes = {}

    # Initialize BFS queue with allocated nodes
    for node_id in allocated_node_ids:
        queue.append((node_id, [node_id], 0))

    while queue:
        current_id, path, depth = queue.pop(0)
        if depth >= max_depth:
            continue

        neighbors = connections.get(current_id, [])
        for neighbor_id in neighbors:
            if neighbor_id in visited:
                continue
            visited.add(neighbor_id)
            new_path = path + [neighbor_id]
            if neighbor_id not in allocated_set:
                # Save first path found to this unallocated node
                if neighbor_id not in future_nodes:
                    future_nodes[neighbor_id] = new_path
            queue.append((neighbor_id, new_path, depth + 1))

    return future_nodes

    return score

def find_path(nodes, connections, start_node_id, target_node_ids, max_points=MAX_POINTS):
    distances = {node_id: float("inf") for node_id in nodes}
    distances[start_node_id] = 0
    previous = {node_id: None for node_id in nodes}
    pq = [(0, start_node_id)]
    points_spent = 0
    visited = set()
    print(f"Starting pathfinding from {start_node_id} to {target_node_ids}")
    
    while pq and points_spent < max_points:
        dist, current = heapq.heappop(pq)
        if current in visited:
            continue
        visited.add(current)
        points_spent += 1
        print(f"Visited {current}, points spent: {points_spent}")
        
        if current in target_node_ids:
            print(f"Reached target {current}")
            target_node_ids.remove(current)
            if not target_node_ids:
                print("All targets reached")
                break
        
        neighbors = connections[current]
        print(f"Neighbors of {current}: {neighbors}")
        for neighbor_id in neighbors:
            if neighbor_id in visited:
                continue
            new_dist = dist + 1
            if new_dist < distances[neighbor_id]:
                distances[neighbor_id] = new_dist
                previous[neighbor_id] = current
                heapq.heappush(pq, (new_dist, neighbor_id))
    
    paths = []
    for target_id in target_node_ids.copy():
        if distances[target_id] == float("inf"):
            print(f"Could not reach target {target_id}")
            target_node_ids.remove(target_id)
            continue
        path = []
        current = target_id
        while current is not None:
            path.append(current)
            current = previous[current]
        path.reverse()
        paths.append(path)
    
    if not paths:
        print("No paths found")
        return []
    
    main_path = paths[0]
    for path in paths[1:]:
        i = 0
        while i < len(main_path) and i < len(path) and main_path[i] == path[i]:
            i += 1
        main_path.extend(path[i:])
    
    seen = set()
    final_path = []
    for node_id in main_path:
        if node_id not in seen:
            seen.add(node_id)
            final_path.append(node_id)
    print(f"Final path: {final_path}")
    return final_path

def process_gems_for_chroma(gem_list):
    """Processes raw gem data into a list of dictionaries for ChromaDB."""
    processed_gems = []
    id_counter = collections.Counter()

    for gem in gem_list:
        # Ensure basic fields exist and handle None
        name = gem.get("name", "Unknown Gem")
        # Attempt to convert level to int, default to 1 if invalid or missing
        try:
            level = int(gem.get("level", 1))
        except (ValueError, TypeError):
            level = 1
        tags = gem.get("tags", "") or ""
        description = gem.get("description", "") or ""
        gem_type = gem.get("type", "Unknown") or "Unknown"
        primary_role = gem.get("primary_role", "Unknown") or "Unknown"

        # Create a base ID
        base_id = f"{name}_lvl{level}"
        id_counter[base_id] += 1
        unique_id = f"{base_id}_{id_counter[base_id]}" if id_counter[base_id] > 1 else base_id

        # Create the document string
        document = f"Name: {name}\nLevel: {level}\nType: {gem_type}\nTags: {tags}\nRole: {primary_role}\nDescription: {description}"

        # Create metadata dictionary, ensuring values are valid types
        metadata = {
            "name": str(name),
            "level": level, # Already int
            "tags": str(tags),
            "type": str(gem_type),
            "primary_role": str(primary_role),
            # Add any other relevant fields from the gem dict if needed
        }
        # Clean metadata: replace None or ensure correct types (mostly handled by gets/defaults)
        for key, value in metadata.items():
             if value is None:
                 metadata[key] = "" # Default to empty string for None
             elif not isinstance(value, (str, int, float, bool)):
                 metadata[key] = str(value) # Convert other types to string


        processed_gems.append({
            "id": unique_id,
            "document": document,
            "metadata": metadata
        })

    return processed_gems

def process_passives_for_chroma(passive_list):
    """Processes raw passive skill data into a list of dictionaries for ChromaDB."""
    processed_passives = []
    # Header reminder: Node ID | Name | Stats | X | Y | Group | Is Notable | Is Keystone | Is Mastery | Class Start Index | Ascendancy Name
    # Indices:        0       | 1    | 2     | 3 | 4 | 5     | 6          | 7           | 8          | 9                 | 10

    for i, node in enumerate(passive_list):
        # --- Data Extraction with Type Conversion and Defaults ---
        try:
            node_id = str(node[0]) if len(node) > 0 and node[0] is not None else f"missing_node_id_{i}"
        except IndexError: node_id = f"missing_node_id_{i}"

        try:
            name = str(node[1]) if len(node) > 1 and node[1] is not None else "Unknown Passive"
        except IndexError: name = "Unknown Passive"

        try:
            stats = str(node[2]) if len(node) > 2 and node[2] is not None else ""
        except IndexError: stats = ""

        try:
            x = float(node[3]) if len(node) > 3 and node[3] is not None else 0.0
        except (IndexError, ValueError, TypeError): x = 0.0

        try:
            y = float(node[4]) if len(node) > 4 and node[4] is not None else 0.0
        except (IndexError, ValueError, TypeError): y = 0.0

        try:
            # Assuming group is int, handle potential non-numeric values if necessary
            group = int(node[5]) if len(node) > 5 and node[5] is not None else 0
        except (IndexError, ValueError, TypeError): group = 0

        try:
            # Explicitly check for truthy values for booleans
            is_notable = bool(node[6]) if len(node) > 6 and node[6] is not None else False
        except IndexError: is_notable = False

        try:
            is_keystone = bool(node[7]) if len(node) > 7 and node[7] is not None else False
        except IndexError: is_keystone = False

        try:
            is_mastery = bool(node[8]) if len(node) > 8 and node[8] is not None else False
        except IndexError: is_mastery = False

        # class_start_index = int(node[9]) if len(node) > 9 and node[9] is not None else -1 # Might not be needed for Chroma?

        try:
            ascendancy_name = str(node[10]) if len(node) > 10 and node[10] is not None else ""
        except IndexError: ascendancy_name = ""


        # --- Create Document String ---
        document_parts = [f"Name: {name}"]
        if stats:
            document_parts.append(f"Stats: {stats}")
        if is_notable:
            document_parts.append("Type: Notable")
        if is_keystone:
            document_parts.append("Type: Keystone")
        if is_mastery:
             document_parts.append("Type: Mastery")
        if ascendancy_name:
            document_parts.append(f"Ascendancy: {ascendancy_name}")
        document = "\n".join(document_parts)


        # --- Create Metadata Dictionary ---
        metadata = {
            "node_id": node_id,
            "name": name,
            "stats": stats,
            "x": x,
            "y": y,
            "group": group,
            "is_notable": is_notable,
            "is_keystone": is_keystone,
            "is_mastery": is_mastery,
            "ascendancy_name": ascendancy_name,
            # Add other relevant fields if needed
        }
         # Clean metadata (mostly handled by extraction, but ensures no None slips through)
        for key, value in metadata.items():
             if value is None:
                 # Assign default based on expected type
                 if isinstance(metadata[key], bool): metadata[key] = False
                 elif isinstance(metadata[key], (int, float)): metadata[key] = 0
                 else: metadata[key] = ""
             elif not isinstance(value, (str, int, float, bool)):
                 metadata[key] = str(value) # Convert unexpected types


        processed_passives.append({
            "id": node_id, # Node ID is expected to be unique
            "document": document,
            "metadata": metadata
        })

    return processed_passives


def get_adjacent_unallocated_nodes(allocated_path_ids: list, all_nodes: dict, connections: dict):
    """
    Finds all passive skill nodes that are directly adjacent to the *last* node in the allocated path
    but are not allocated themselves.

    Args:
        allocated_path_ids (list): An ordered list of IDs for the nodes in the current path.
        all_nodes (dict): A dictionary mapping node IDs to node data (including name, stats).
        connections (dict): A dictionary mapping node IDs to a list of adjacent node IDs.

    Returns:
        list: A list of dictionaries, where each dictionary contains the details
              (id, name, stats) of an adjacent, unallocated node.
    """
    if not allocated_path_ids:
        return [] # No path, no adjacent nodes

    last_node_id = allocated_path_ids[-1]
    allocated_set = set(allocated_path_ids) # Keep the set for efficient filtering later
    adjacent_nodes = set()

    # 1. Direct lookup (neighbors listed under last_node_id)
    direct_neighbors = connections.get(last_node_id, [])
    if isinstance(direct_neighbors, list):
        adjacent_nodes.update(direct_neighbors)
    else:
        print(f"Warning: Neighbors data for node {last_node_id} is not a list: {direct_neighbors}")

    # 2. Reverse lookup (nodes that list last_node_id as a neighbor)
    for node_id, neighbors_list in connections.items():
        # Check if the current node_id is the last_node itself to avoid self-loops if data is inconsistent
        if node_id == last_node_id:
            continue
        if isinstance(neighbors_list, list) and last_node_id in neighbors_list:
            adjacent_nodes.add(node_id)

    # The filtering logic from line 342 onwards uses 'adjacent_nodes' and needs the allocated set
    # Note: The original line 342 used 'allocated_node_ids', which was a set. We now use 'allocated_set'.

    # Filter out nodes that are already allocated
    unallocated_adjacent_ids = adjacent_nodes - allocated_set

    # Get the details for the unallocated adjacent nodes
    result_nodes = []
    for node_id in unallocated_adjacent_ids:
        if node_id in all_nodes:
            node_data = all_nodes[node_id]
            result_nodes.append({
                "id": node_id,
                "name": node_data.get("name", "Unknown Node"),
                "stats": node_data.get("stats", "")
                # Add other relevant fields if needed, e.g., 'is_notable'
            })
        else:
            # Handle cases where an adjacent node ID might not be in all_nodes (shouldn't happen ideally)
            print(f"Warning: Adjacent node ID {node_id} not found in all_nodes data.")


    # Sort nodes alphabetically by name for consistent presentation
    result_nodes.sort(key=lambda x: x["name"])

    return result_nodes

def calculate_accumulated_stats(selected_nodes: list, all_nodes: dict):
    """
    Calculates the aggregated stats from a list of selected node objects.
    Handles attribute choices (+5 Str/Dex/Int) for specific nodes.
    """
    stat_map = defaultdict(float) # Use float for potential future non-integer stats
    stat_types = {} # To store 'percent_increase', 'percent', 'flat', 'flat_other', 'count'

    if not all_nodes:
        return "Node data not available."

    for node_info in selected_nodes:
        node_id = node_info.get('id')
        choice = node_info.get('choice') # Get the choice ('Strength', 'Dexterity', 'Intelligence', or null)

        if not node_id:
            continue # Skip if node info is invalid

        # Handle attribute choices directly
        if choice == 'Strength':
            stat_map['Strength'] += 5
            stat_types['Strength'] = 'flat'
            # Don't process regular stats for this node if a choice was made
            continue
        elif choice == 'Dexterity':
            stat_map['Dexterity'] += 5
            stat_types['Dexterity'] = 'flat'
            continue
        elif choice == 'Intelligence':
            stat_map['Intelligence'] += 5
            stat_types['Intelligence'] = 'flat'
            continue
        # Else (choice is null or missing), process the node's regular stats
        else:
            node = all_nodes.get(node_id)
            if not node:
                continue # Skip if node data not found in all_nodes

            # Ensure stats is a list or convert string
            node_stats_list = node.get("stats", [])
            if isinstance(node_stats_list, str):
                 # Split stats string if it's comma-separated, handle empty string
                 node_stats_list = [s.strip() for s in node_stats_list.split(',')] if node_stats_list else []
            elif not isinstance(node_stats_list, list):
                 node_stats_list = [] # Ignore if not string or list

            for stat_str in node_stats_list:
                stat_str = stat_str.strip()
                if not stat_str or stat_str.lower() == "none":
                    continue

                # Regex patterns (adjust as needed based on exact stat formats)
                # Pattern 1: X% increased Stat Name
                match = re.match(r"(\d+(?:\.\d+)?)%?\s+increased\s+(.+)", stat_str, re.IGNORECASE)
                if match:
                    value = float(match.group(1))
                    stat_name = match.group(2).strip()
                    stat_map[stat_name] += value
                    stat_types[stat_name] = "percent_increase"
                    continue

                # Pattern 2: +X to Stat Name (Ensure it doesn't match the attribute choices already handled)
                match = re.match(r"\+(\d+(?:\.\d+)?)\s+to\s+(.+)", stat_str, re.IGNORECASE)
                if match:
                    value = float(match.group(1))
                    stat_name = match.group(2).strip()
                    # Avoid double-counting base attributes if they appear in node text
                    if stat_name not in ['Strength', 'Dexterity', 'Intelligence']:
                        stat_map[stat_name] += value
                        stat_types[stat_name] = "flat"
                    continue

                # Pattern 3: X% Stat Name (e.g., Attack Speed, Resistance)
                # Make % optional and handle potential missing space
                match = re.match(r"(\d+(?:\.\d+)?)%?\s*(.+)", stat_str, re.IGNORECASE)
                if match:
                    value = float(match.group(1))
                    stat_name = match.group(2).strip()
                    stat_map[stat_name] += value
                    # Determine type based on presence of %
                    stat_types[stat_name] = "percent" if '%' in stat_str else "flat_other"
                    continue

                # Fallback for stats without numbers (like Keystones - count occurrences)
                stat_name = stat_str
                stat_map[stat_name] += 1
                stat_types[stat_name] = "count"

    # Format the stats into a string for the LLM
    stats_lines = []
    # Sort by stat name for consistency
    for stat_name in sorted(stat_map.keys()):
        value = stat_map[stat_name]
        stat_type = stat_types.get(stat_name, "unknown")

        # Format based on type
        if stat_type == "percent_increase":
            stats_lines.append(f"{stat_name}: {value:g}% increased")
        elif stat_type == "percent":
             stats_lines.append(f"{stat_name}: {value:g}%")
        elif stat_type == "flat":
            stats_lines.append(f"{stat_name}: +{value:g}")
        elif stat_type == "flat_other":
             stats_lines.append(f"{stat_name}: {value:g}") # e.g., Maximum Rage
        elif stat_type == "count":
            # Simplified: Just list the stat name for counts (implies presence)
            # If count > 1 is needed, add: if value > 1: stats_lines.append(f"{stat_name} (x{int(value)})") else: stats_lines.append(stat_name)
            stats_lines.append(f"{stat_name}") # List the name (e.g., "Resolute Technique")
        else:
            stats_lines.append(f"{stat_name}: {value:g} (Unknown Type)")

    return "\n".join(stats_lines) if stats_lines else "No stats accumulated yet."
