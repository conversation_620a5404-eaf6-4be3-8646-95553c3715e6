import os
import requests

def search_web(query, max_results=3):
    """
    Performs a web search using Brave Search API only, restricting results to Path of Exile 2 (POE2) context.
    Returns a summary string of the top results.
    """
    brave_key = os.getenv("BRAVE_API_KEY")
    if not brave_key:
        return "Brave Search API key (BRAVE_API_KEY) is not set. Please set your Brave API key in the environment."
    try:
        # Always bias the query to POE2 context
        poe2_query = f"{query} Path of Exile 2"
        url = "https://api.search.brave.com/res/v1/web/search"
        headers = {"Accept": "application/json", "X-Subscription-Token": brave_key}
        params = {"q": poe2_query, "count": max_results}
        resp = requests.get(url, headers=headers, params=params, timeout=8)
        resp.raise_for_status()
        data = resp.json()
        results = []
        for res in data.get("web", {}).get("results", [])[:max_results]:
            title = res.get("title", "")
            desc = res.get("description", "")
            link = res.get("url", "")
            if title or desc:
                results.append(f"{title}: {desc} ({link})")
        if results:
            return " | ".join(results)
        return "No relevant web summary found from Brave Search for this query."
    except Exception as e:
        return f"Brave Search failed: {e}"